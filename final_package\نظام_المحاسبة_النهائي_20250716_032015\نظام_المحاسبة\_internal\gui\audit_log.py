from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QLabel, QComboBox,
                           QDateEdit, QFrame, QHeaderView)
from PyQt5.QtCore import Qt, QDate
from sqlalchemy.orm import Session
from database.users import User, AuditLog
from database.audit import AuditSystem
from datetime import datetime, timedelta

class AuditLogDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.audit_system = AuditSystem(engine)
        self.setup_ui()
        self.load_logs()
        
    def setup_ui(self):
        self.setWindowTitle("سجل العمليات")
        self.setMinimumSize(1000, 600)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
            QTableWidget {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
            }
            QHeaderView::section {
                background-color: #F8F9FA;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
            QComboBox, QDateEdit {
                padding: 5px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 150px;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton[primary="true"] {
                background-color: #0D6EFD;
                color: white;
            }
        """)

        layout = QVBoxLayout()

        # إطار الفلترة
        filter_frame = QFrame()
        filter_layout = QHBoxLayout()
        
        # فلتر المستخدمين
        self.user_filter = QComboBox()
        self.user_filter.addItem("جميع المستخدمين")
        with Session(self.engine) as session:
            users = session.query(User).all()
            for user in users:
                self.user_filter.addItem(user.username, user.id)
        
        # فلتر نوع العملية
        self.action_filter = QComboBox()
        self.action_filter.addItems([
            "جميع العمليات",
            "تسجيل دخول",
            "إضافة",
            "تعديل",
            "حذف",
            "طباعة",
            "تصدير"
        ])
        
        # فلتر التاريخ
        self.date_filter = QComboBox()
        self.date_filter.addItems([
            "جميع الفترات",
            "اليوم",
            "آخر أسبوع",
            "آخر شهر",
            "فترة محددة"
        ])
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.start_date.hide()
        self.end_date.hide()
        
        # زر التحديث
        refresh_btn = QPushButton("تحديث")
        refresh_btn.setProperty("primary", True)
        refresh_btn.clicked.connect(self.load_logs)
        
        filter_layout.addWidget(QLabel("المستخدم:"))
        filter_layout.addWidget(self.user_filter)
        filter_layout.addWidget(QLabel("نوع العملية:"))
        filter_layout.addWidget(self.action_filter)
        filter_layout.addWidget(QLabel("الفترة:"))
        filter_layout.addWidget(self.date_filter)
        filter_layout.addWidget(self.start_date)
        filter_layout.addWidget(self.end_date)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        
        filter_frame.setLayout(filter_layout)
        layout.addWidget(filter_frame)

        # جدول السجل
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(6)
        self.log_table.setHorizontalHeaderLabels([
            "التاريخ والوقت",
            "المستخدم",
            "العملية",
            "الجدول",
            "رقم السجل",
            "التفاصيل"
        ])
        
        header = self.log_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Stretch)
        
        layout.addWidget(self.log_table)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        export_btn = QPushButton("تصدير السجل")
        export_btn.clicked.connect(self.export_logs)
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        
        buttons_layout.addWidget(export_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # ربط الأحداث
        self.date_filter.currentIndexChanged.connect(self.on_date_filter_changed)
        
    def on_date_filter_changed(self, index):
        show_custom = self.date_filter.currentText() == "فترة محددة"
        self.start_date.setVisible(show_custom)
        self.end_date.setVisible(show_custom)

    def load_logs(self):
        try:
            # تحديد فترة البحث
            if self.date_filter.currentText() == "اليوم":
                start_date = datetime.now().replace(hour=0, minute=0, second=0)
                end_date = datetime.now()
            elif self.date_filter.currentText() == "آخر أسبوع":
                start_date = datetime.now() - timedelta(days=7)
                end_date = datetime.now()
            elif self.date_filter.currentText() == "آخر شهر":
                start_date = datetime.now() - timedelta(days=30)
                end_date = datetime.now()
            elif self.date_filter.currentText() == "فترة محددة":
                start_date = self.start_date.date().toPyDate()
                end_date = self.end_date.date().toPyDate()
            else:
                start_date = None
                end_date = None

            with Session(self.engine) as session:
                query = session.query(AuditLog).join(User)
                
                # تطبيق الفلاتر
                if self.user_filter.currentText() != "جميع المستخدمين":
                    query = query.filter(AuditLog.user_id == self.user_filter.currentData())
                    
                if self.action_filter.currentText() != "جميع العمليات":
                    query = query.filter(AuditLog.action == self.action_filter.currentText())
                    
                if start_date and end_date:
                    query = query.filter(AuditLog.timestamp.between(start_date, end_date))
                    
                logs = query.order_by(AuditLog.timestamp.desc()).all()
                
                self.log_table.setRowCount(len(logs))
                for row, log in enumerate(logs):
                    self.log_table.setItem(row, 0, QTableWidgetItem(
                        log.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                    ))
                    self.log_table.setItem(row, 1, QTableWidgetItem(log.user.username))
                    self.log_table.setItem(row, 2, QTableWidgetItem(log.action))
                    self.log_table.setItem(row, 3, QTableWidgetItem(log.table_name or ""))
                    self.log_table.setItem(row, 4, QTableWidgetItem(
                        str(log.record_id) if log.record_id else ""
                    ))
                    self.log_table.setItem(row, 5, QTableWidgetItem(log.details or ""))
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل السجل: {str(e)}")

    def export_logs(self):
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير السجل", "", "Excel Files (*.xlsx);;CSV Files (*.csv)"
            )
            
            if not filename:
                return
                
            if filename.endswith('.xlsx'):
                self.export_to_excel(filename)
            else:
                self.export_to_csv(filename)
                
            QMessageBox.information(self, "نجاح", "تم تصدير السجل بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير السجل: {str(e)}")

    def export_to_excel(self, filename):
        import xlsxwriter
        
        workbook = xlsxwriter.Workbook(filename)
        worksheet = workbook.add_worksheet()
        
        # تنسيقات
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#F8F9FA',
            'border': 1
        })
        
        # كتابة الترويسة
        headers = ["التاريخ والوقت", "المستخدم", "العملية", "الجدول", "رقم السجل", "التفاصيل"]
        for col, header in enumerate(headers):
            worksheet.write(0, col, header, header_format)
            
        # كتابة البيانات
        for row in range(self.log_table.rowCount()):
            for col in range(self.log_table.columnCount()):
                item = self.log_table.item(row, col)
                worksheet.write(row + 1, col, item.text() if item else "")
                
        workbook.close()

    def export_to_csv(self, filename):
        import csv
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as file:
            writer = csv.writer(file)
            
            # كتابة الترويسة
            headers = ["التاريخ والوقت", "المستخدم", "العملية", "الجدول", "رقم السجل", "التفاصيل"]
            writer.writerow(headers)
            
            # كتابة البيانات
            for row in range(self.log_table.rowCount()):
                row_data = []
                for col in range(self.log_table.columnCount()):
                    item = self.log_table.item(row, col)
                    row_data.append(item.text() if item else "")
                writer.writerow(row_data)