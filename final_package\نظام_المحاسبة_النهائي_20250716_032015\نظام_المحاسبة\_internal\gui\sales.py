from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QComboBox, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox, QDialog,
                             QFrame, QGridLayout, QHeaderView, QDateEdit, QFormLayout, QShortcut)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QColor, QFont, QPixmap, QKeySequence
from sqlalchemy.orm import Session
from database.models import Transaction, TransactionItem, Product, Customer, TransactionType, ProductBarcode
from utils.advanced_invoice_printer import show_advanced_print_dialog
from utils.currency_formatter import format_currency, format_number
from utils.theme_manager import theme_manager

class ProductSelectionDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setWindowTitle("إضافة منتج للفاتورة")

        # إضافة أزرار التحكم في النافذة (تكبير/تصغير)
        self.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)

        self.setMinimumSize(500, 400)
        self.resize(600, 500)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # منطقة البحث عن المنتج
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث عن المنتج...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
            }
        """)
        search_layout.addWidget(self.search_input)

        # أزرار مسح الباركود في نافذة اختيار المنتج
        scan_camera_btn = QPushButton("📷")
        scan_camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 8px;
                border: none;
                border-radius: 4px;
                min-width: 40px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        scan_camera_btn.setToolTip("مسح الباركود بالكاميرا")
        scan_camera_btn.clicked.connect(self.scan_barcode_camera)
        search_layout.addWidget(scan_camera_btn)

        scan_device_btn = QPushButton("🔍")
        scan_device_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 8px;
                border: none;
                border-radius: 4px;
                min-width: 40px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        scan_device_btn.setToolTip("مسح الباركود بالجهاز المحمول")
        scan_device_btn.clicked.connect(self.scan_barcode_device)
        search_layout.addWidget(scan_device_btn)

        layout.addLayout(search_layout)
        
        # قائمة المنتجات
        self.products_list = QTableWidget()
        self.products_list.setColumnCount(4)
        self.products_list.setHorizontalHeaderLabels(["الكود", "المنتج", "السعر", "المتوفر"])
        self.products_list.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        layout.addWidget(self.products_list)
        
        # منطقة تحديد الكمية والسعر
        details_frame = QFrame()
        details_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        details_layout = QGridLayout()
        details_frame.setLayout(details_layout)
        
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMaximum(1000)
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setMaximum(1000000)
        
        details_layout.addWidget(QLabel("الكمية:"), 0, 0)
        details_layout.addWidget(self.quantity_spin, 0, 1)
        details_layout.addWidget(QLabel("السعر:"), 0, 2)
        details_layout.addWidget(self.price_spin, 0, 3)
        
        layout.addWidget(details_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        confirm_btn = QPushButton("إضافة للفاتورة")
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        confirm_btn.clicked.connect(self.validate_and_accept)
        cancel_btn.clicked.connect(self.reject)
        self.search_input.textChanged.connect(self.search_products)
        self.products_list.itemSelectionChanged.connect(self.on_product_selected)
        
        # تحميل المنتجات
        self.load_products()
        
    def load_products(self):
        with Session(self.engine) as session:
            products = session.query(Product).all()
            self.products_list.setRowCount(len(products))
            
            for row, product in enumerate(products):
                self.products_list.setItem(row, 0, QTableWidgetItem(str(product.id)))
                self.products_list.setItem(row, 1, QTableWidgetItem(product.name))
                self.products_list.setItem(row, 2, QTableWidgetItem(str(product.sale_price)))
                self.products_list.setItem(row, 3, QTableWidgetItem(str(product.quantity)))
                
    def search_products(self):
        search_text = self.search_input.text().lower()
        for row in range(self.products_list.rowCount()):
            product_name = self.products_list.item(row, 1).text().lower()
            self.products_list.setRowHidden(row, search_text not in product_name)
            
    def on_product_selected(self):
        selected_rows = self.products_list.selectedItems()
        if selected_rows:
            row = selected_rows[0].row()
            price = float(self.products_list.item(row, 2).text())
            max_quantity = int(self.products_list.item(row, 3).text())
            
            self.price_spin.setValue(price)
            self.quantity_spin.setMaximum(max_quantity)
            
    def scan_barcode_camera(self):
        """مسح الباركود بالكاميرا في نافذة اختيار المنتج"""
        try:
            from .barcode_scanner import BarcodeScannerDialog

            scanner = BarcodeScannerDialog(self)
            scanner.barcode_detected.connect(self.on_barcode_detected_in_dialog)
            scanner.exec_()

        except ImportError as e:
            QMessageBox.warning(
                self,
                "مكتبات مفقودة",
                "لاستخدام قارئ الباركود بالكاميرا، يجب تثبيت المكتبات التالية:\n"
                "pip install opencv-python pyzbar\n\n"
                f"تفاصيل الخطأ: {str(e)}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح قارئ الباركود:\n{str(e)}"
            )

    def scan_barcode_device(self):
        """مسح الباركود بالجهاز المحمول في نافذة اختيار المنتج"""
        from PyQt5.QtWidgets import QInputDialog

        barcode, ok = QInputDialog.getText(
            self,
            "مسح الباركود",
            "امسح الباركود أو اكتبه:",
            text=""
        )

        if ok and barcode.strip():
            self.on_barcode_detected_in_dialog(barcode.strip())

    def on_barcode_detected_in_dialog(self, barcode):
        """معالجة الباركود المكتشف في نافذة اختيار المنتج"""
        try:
            with Session(self.engine) as session:
                # البحث في الباركود الأساسي أولاً
                product = session.query(Product).filter(Product.barcode == barcode).first()

                # إذا لم يوجد، ابحث في الباركودات المتعددة
                if not product:
                    barcode_record = session.query(ProductBarcode).filter(
                        ProductBarcode.barcode == barcode
                    ).first()
                    if barcode_record:
                        product = barcode_record.product

                if product:
                    # البحث عن المنتج في الجدول وتحديده
                    for row in range(self.products_list.rowCount()):
                        if int(self.products_list.item(row, 0).text()) == product.id:
                            self.products_list.selectRow(row)
                            self.products_list.scrollToItem(self.products_list.item(row, 0))

                            # تحديث السعر والكمية
                            self.price_spin.setValue(product.sale_price)
                            self.quantity_spin.setMaximum(product.quantity)
                            self.quantity_spin.setValue(1)

                            QMessageBox.information(
                                self,
                                "✅ تم العثور على المنتج",
                                f"تم تحديد المنتج: {product.name}"
                            )
                            return

                    QMessageBox.warning(
                        self,
                        "⚠️ المنتج غير مرئي",
                        f"المنتج '{product.name}' موجود لكنه مخفي بسبب البحث.\nامسح البحث لرؤية جميع المنتجات."
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "❌ منتج غير موجود",
                        f"لم يتم العثور على منتج بالباركود: {barcode}"
                    )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء البحث عن المنتج:\n{str(e)}"
            )

    def validate_and_accept(self):
        """التحقق من صحة السعر قبل الموافقة"""
        selected_rows = self.products_list.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج أولاً")
            return

        try:
            row = selected_rows[0].row()
            product_id = int(self.products_list.item(row, 0).text())
            price = self.price_spin.value()

            # التحقق من أن السعر مربح
            with Session(self.engine) as session:
                product = session.query(Product).get(product_id)
                if product:
                    # فحص الحد الأدنى للبيع أولاً
                    min_sale_price = getattr(product, 'min_sale_price', 0) or 0
                    if min_sale_price > 0 and price < min_sale_price:
                        QMessageBox.warning(
                            self,
                            "🚫 تحذير: أقل من الحد الأدنى للبيع",
                            f"لا يمكن إضافة المنتج بهذا السعر!\n\n"
                            f"📦 المنتج: {product.name}\n"
                            f"🔴 السعر المطلوب: {price:,.0f} ج.م\n"
                            f"⚠️ الحد الأدنى للبيع: {min_sale_price:,.0f} ج.م\n\n"
                            f"💡 يجب أن يكون السعر أكبر من أو يساوي الحد الأدنى المحدد"
                        )
                        return

                    # فحص أن السعر أكبر من سعر الشراء
                    if price <= product.purchase_price:
                        QMessageBox.warning(
                            self,
                            "⚠️ تحذير: سعر غير مربح",
                            f"لا يمكن إضافة المنتج بهذا السعر!\n\n"
                            f"📦 المنتج: {product.name}\n"
                            f"💰 سعر الشراء: {product.purchase_price:,.0f} ج.م\n"
                            f"🔴 السعر المطلوب: {price:,.0f} ج.م\n\n"
                            f"⚠️ سعر البيع يجب أن يكون أكبر من سعر الشراء لضمان الربحية!\n"
                            f"💡 الحد الأدنى المسموح: {product.purchase_price + 1:,.0f} ج.م"
                        )
                        return

            # إذا كان السعر صحيح، قبول النافذة
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من السعر:\n{str(e)}")

    def get_selected_product(self):
        if self.exec_() == QDialog.Accepted:
            selected_rows = self.products_list.selectedItems()
            if selected_rows:
                row = selected_rows[0].row()
                product_id = int(self.products_list.item(row, 0).text())
                return product_id, self.quantity_spin.value(), self.price_spin.value()
        return None, None, None

class SalesWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.items = []
        self.setup_ui()

    def is_dark_mode(self):
        """التحقق من الوضع الليلي"""
        if hasattr(self.parent(), 'is_dark_mode'):
            return self.parent().is_dark_mode
        return False

    def get_table_style(self):
        """الحصول على تنسيق الجدول حسب الوضع"""
        if self.is_dark_mode():
            # ألوان الوضع الليلي
            return """
                QTableWidget {
                    border: 2px solid #334155;
                    background-color: #0F172A;
                    gridline-color: #334155;
                    font-size: 16px;
                    color: #F8FAFC;
                    border-radius: 12px;
                }
                QTableWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #334155;
                    color: #F8FAFC;
                }
                QTableWidget::item:selected {
                    background-color: #6366F1;
                    color: #F8FAFC;
                }
                QTableWidget::item:hover {
                    background-color: rgba(99, 102, 241, 0.1);
                }
                QHeaderView::section {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #EF4444, stop:1 #F87171);
                    color: #F8FAFC;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }
                QHeaderView::section:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #DC2626, stop:1 #EF4444);
                }
            """
        else:
            # ألوان الوضع العادي
            return """
                QTableWidget {
                    border: 1px solid #DEE2E6;
                    background-color: white;
                    gridline-color: #DEE2E6;
                    font-size: 16px;
                    color: #212529;
                    border-radius: 8px;
                }
                QTableWidget::item {
                    padding: 12px;
                    border-bottom: 1px solid #DEE2E6;
                    color: #212529;
                }
                QTableWidget::item:selected {
                    background-color: #E3F2FD;
                    color: #1976D2;
                }
                QTableWidget::item:hover {
                    background-color: rgba(25, 118, 210, 0.1);
                }
                QHeaderView::section {
                    background-color: #2980B9;
                    color: white;
                    padding: 15px;
                    border: none;
                    font-weight: bold;
                    font-size: 18px;
                    border-radius: 8px;
                }
                QHeaderView::section:hover {
                    background-color: #3498DB;
                }
            """

    def get_row_colors(self, row):
        """الحصول على ألوان الصفوف حسب الوضع"""
        if self.is_dark_mode():
            return QColor("#1E293B") if row % 2 == 0 else QColor("#334155")
        else:
            return QColor("#F8F9FA") if row % 2 == 0 else QColor("#FFFFFF")

    def update_theme(self):
        """تحديث الألوان عند تغيير الوضع"""
        # تطبيق الثيم العام
        self.setStyleSheet(theme_manager.get_stylesheet("general"))

        # تحديث تنسيق الجدول
        self.products_table.setStyleSheet(theme_manager.get_stylesheet("table"))

        # تحديث الأزرار
        colors = theme_manager.get_colors()

        # تحديث إطار العنوان
        if hasattr(self, 'header_frame'):
            header_bg = colors['primary'] if theme_manager.is_dark_mode() else "#2980B9"
            self.header_frame.setStyleSheet(f"""
                QFrame {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {header_bg}, stop:1 {colors['primary']});
                    border-radius: 10px;
                    padding: 20px;
                    margin-bottom: 10px;
                }}
                QLabel {{ color: white; }}
                QComboBox {{
                    background-color: rgba(255, 255, 255, 0.9);
                    padding: 8px;
                    border: none;
                    border-radius: 5px;
                    min-width: 250px;
                }}
            """)

        # تحديث إطار البحث
        if hasattr(self, 'search_frame'):
            self.search_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {colors['card_bg']};
                    border: 1px solid {colors['border_color']};
                    border-radius: 8px;
                    padding: 10px;
                    margin-bottom: 10px;
                }}
            """)

        # إعادة تحميل البيانات لتطبيق الألوان الجديدة
        self.update_total()

    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            with Session(self.engine) as session:
                customers = session.query(Customer).filter(Customer.is_active == True).all()
                self.customer_combo.clear()

                # الخيار الافتراضي للفواتير السريعة
                self.customer_combo.addItem("💳 فاتورة نقدية سريعة (بدون عميل)", None)

                if customers:
                    # إضافة خط فاصل
                    self.customer_combo.addItem("─────────────────────", None)

                    for customer in customers:
                        display_text = f"👤 {customer.name}"
                        if hasattr(customer, 'phone') and customer.phone:
                            display_text += f" - {customer.phone}"
                        self.customer_combo.addItem(display_text, customer.id)
                else:
                    # إذا لم توجد عملاء
                    self.customer_combo.addItem("💡 لا توجد عملاء مسجلين", None)

        except Exception as e:
            print(f"Error loading customers: {e}")
            QMessageBox.warning(self, "تنبيه", f"حدث خطأ أثناء تحميل قائمة العملاء:\n{str(e)}")
            # إضافة عميل افتراضي في حالة الخطأ
            self.customer_combo.clear()
            self.customer_combo.addItem("💳 فاتورة نقدية سريعة", None)
            self.customer_combo.addItem("❌ خطأ في تحميل العملاء", None)

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Initialize customer combo box first
        self.customer_combo = QComboBox()
        self.customer_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
                min-width: 250px;
            }
        """)
        
        # Initialize paid amount field
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setMaximum(1000000)
        self.paid_amount.setDecimals(0)  # إزالة الخانات العشرية
        self.paid_amount.valueChanged.connect(self.update_total)
        
        # Initialize discount field (will be created in update_summary_section)
        # الخصم سيتم إنشاؤه في قسم الملخص
        
        # Load customers after initializing the combo box
        self.load_customers()
        
        # تحسين العنوان والمعلومات الأساسية
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(41, 128, 185, 0.1);
                border: 2px solid white;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 10px;
            }
            QLabel { color: white; font-weight: bold; }
            QComboBox {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
                min-width: 250px;
            }
        """)
        header_layout = QGridLayout()
        header_frame.setLayout(header_layout)

        # إضافة شعار المبيعات
        sales_icon = QLabel()
        sales_icon_pixmap = QPixmap("images/sales.png")
        if not sales_icon_pixmap.isNull():
            sales_icon.setPixmap(sales_icon_pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
        # تحسين عنوان الفاتورة
        invoice_label = QLabel("فاتورة مبيعات جديدة")
        invoice_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")

        # إضافة رقم الفاتورة التلقائي
        self.invoice_number = QLabel()
        self.generate_invoice_number()
        self.invoice_number.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")

        # خانة المكسب السرية (مخفية افتراضياً) - جنب رقم الفاتورة
        self.profit_value = QLabel("0")
        self.profit_value.setStyleSheet("""
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            background-color: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 3px;
            font-weight: bold;
        """)
        self.profit_value.setVisible(False)  # مخفية افتراضياً
        
        # إضافة التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
            }
        """)
        
        header_layout.addWidget(sales_icon, 0, 0)
        header_layout.addWidget(invoice_label, 0, 1)
        header_layout.addWidget(self.invoice_number, 0, 2)
        header_layout.addWidget(self.profit_value, 0, 3)  # خانة المكسب السرية
        header_layout.addWidget(QLabel("العميل:"), 1, 0)

        # إنشاء layout أفقي للعميل وزر الإضافة
        customer_widget = QWidget()
        customer_layout = QHBoxLayout()
        customer_layout.setContentsMargins(0, 0, 0, 0)
        customer_layout.addWidget(self.customer_combo)

        # زر إضافة عميل جديد
        self.add_customer_btn = QPushButton("➕")
        self.add_customer_btn.setFixedSize(40, 40)
        self.add_customer_btn.setToolTip("إضافة عميل جديد")
        self.add_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 18px;
                font-weight: bold;
                border: none;
                border-radius: 20px;
                margin-left: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #1E7E34;
            }
        """)
        self.add_customer_btn.clicked.connect(self.show_add_customer_dialog)
        customer_layout.addWidget(self.add_customer_btn)

        customer_widget.setLayout(customer_layout)
        header_layout.addWidget(customer_widget, 1, 1)
        header_layout.addWidget(QLabel("التاريخ:"), 1, 2)
        header_layout.addWidget(self.date_edit, 1, 3)
        
        layout.addWidget(header_frame)

        # إضافة شريط البحث السريع
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        search_layout = QHBoxLayout()
        search_frame.setLayout(search_layout)
        
        self.quick_search = QLineEdit()
        self.quick_search.setPlaceholderText("البحث بالاسم أو الكود أو الباركود...")
        self.quick_search.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #3498DB;
                border-radius: 8px;
                min-width: 350px;
                font-size: 16px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2980B9;
                background-color: #F8F9FA;
            }
        """)

        # إعداد القائمة المنسدلة التلقائية
        self.setup_autocomplete()

        # ربط الأحداث
        self.quick_search.textChanged.connect(self.on_search_text_changed)
        self.quick_search.returnPressed.connect(self.on_search_enter)
        
        # أزرار مسح الباركود
        self.scan_camera_btn = QPushButton("📷 مسح بالكاميرا")
        self.scan_camera_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                min-width: 140px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.scan_camera_btn.clicked.connect(self.scan_barcode_camera)

        self.scan_device_btn = QPushButton("🔍 مسح بالجهاز")
        self.scan_device_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                min-width: 140px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.scan_device_btn.clicked.connect(self.scan_barcode_device)
        
        search_layout.addWidget(self.quick_search)
        search_layout.addWidget(self.scan_camera_btn)
        search_layout.addWidget(self.scan_device_btn)
        search_layout.addStretch()
        
        layout.addWidget(search_frame)

        # تحسين جدول المنتجات
        self.products_table = QTableWidget()
        # تطبيق التنسيق حسب الوضع
        self.products_table.setStyleSheet(self.get_table_style())
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels([
            "🛍️ الصنف", "📦 الكمية", "💰 السعر", "💵 السعر النهائي", "🗑️ حذف"
        ])

        # ربط إشارة تغيير البيانات لتحديث السعر النهائي تلقائياً
        self.products_table.itemChanged.connect(self.on_item_changed)
        
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # الصنف - قابل للتمدد
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # الكمية
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # السعر النهائي
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # حذف

        # تحديد عرض الأعمدة مع مساحة أكبر للأسعار لاستيعاب الخط الكبير
        header.resizeSection(1, 120)  # الكمية
        header.resizeSection(2, 180)  # السعر - زيادة العرض لاستيعاب الخط الكبير
        header.resizeSection(3, 200)  # السعر النهائي - زيادة العرض لاستيعاب الخط الكبير
        header.resizeSection(4, 100)  # حذف - مساحة مناسبة للزر
        
        # إضافة خيارات جديدة
        self.add_payment_methods()
        
        layout.addWidget(self.products_table)
        
        # تحسين ملخص الفاتورة
        self.update_summary_section(layout)

    def add_payment_methods(self):
        self.payment_method = QComboBox()
        self.payment_method.addItems(["نقداً", "بطاقة ائتمان", "تحويل بنكي", "شيك"])
        
    def update_summary_section(self, layout):
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 10px;
                padding: 20px;
            }
            QLabel {
                font-size: 16px;
                font-weight: bold;
            }
            .total-label {
                font-size: 20px;
                font-weight: bold;
                color: #2C3E50;
            }
            .remaining-label {
                font-size: 18px;
                font-weight: bold;
                color: #E74C3C;
            }
            QDoubleSpinBox {
                font-size: 16px;
                padding: 8px;
                border: 2px solid #3498DB;
                border-radius: 5px;
                background-color: white;
                min-width: 120px;
            }
            QDoubleSpinBox:focus {
                border-color: #2980B9;
                background-color: #F8F9FA;
            }
        """)

        summary_layout = QGridLayout()

        # المجاميع
        subtotal_label = QLabel("💰 المجموع الفرعي:")
        self.subtotal_value = QLabel("0 ج.م")
        self.subtotal_value.setStyleSheet("color: #27AE60; font-size: 21px; font-weight: bold;")

        # إزالة خانة المكسب من هنا - ستنتقل لمكان آخر

        # حقل الخصم المحسن مع اختيار النوع
        discount_label = QLabel("🎯 الخصم:")

        # اختيار نوع الخصم
        self.discount_type = QComboBox()
        self.discount_type.addItems(["مبلغ", "نسبة %"])
        self.discount_type.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 1px solid #BDC3C7;
                border-radius: 5px;
                padding: 5px;
                font-size: 14px;
            }
        """)
        self.discount_type.currentTextChanged.connect(self.on_discount_type_changed)

        # حقل قيمة الخصم
        self.discount_input = QDoubleSpinBox()
        self.discount_input.setMaximum(999999)
        self.discount_input.setDecimals(0)  # إزالة الخانات العشرية
        self.discount_input.setSuffix(" ج.م")
        self.discount_input.setToolTip("أدخل قيمة الخصم")
        self.discount_input.valueChanged.connect(self.update_total)

        # تسمية لعرض قيمة الخصم المحسوبة
        self.discount_calculated_label = QLabel("")
        self.discount_calculated_label.setStyleSheet("color: #7F8C8D; font-size: 12px; font-style: italic;")

        total_label = QLabel("💵 الإجمالي النهائي:")
        total_label.setProperty("class", "total-label")
        self.total_value = QLabel("0 ج.م")
        self.total_value.setProperty("class", "total-label")
        self.total_value.setStyleSheet("color: #2C3E50; font-size: 26px; font-weight: bold; background-color: #E8F5E8; padding: 10px; border-radius: 5px;")

        # طريقة الدفع
        payment_label = QLabel("💳 طريقة الدفع:")

        # المبلغ المدفوع
        paid_label = QLabel("💸 المدفوع:")

        # المبلغ المتبقي (جديد)
        remaining_label = QLabel("⏳ المتبقي:")
        self.remaining_value = QLabel("0 ج.م")
        self.remaining_value.setProperty("class", "remaining-label")
        self.remaining_value.setStyleSheet("color: #E74C3C; font-size: 23px; font-weight: bold; background-color: #FADBD8; padding: 8px; border-radius: 5px;")

        # ترتيب العناصر في الشبكة
        summary_layout.addWidget(subtotal_label, 0, 0)
        summary_layout.addWidget(self.subtotal_value, 0, 1)
        summary_layout.addWidget(payment_label, 0, 2)
        summary_layout.addWidget(self.payment_method, 0, 3)

        # إنشاء widget مركب للخصم
        discount_widget = QWidget()
        discount_layout = QHBoxLayout()
        discount_layout.setContentsMargins(0, 0, 0, 0)
        discount_layout.setSpacing(5)
        discount_layout.addWidget(self.discount_type)
        discount_layout.addWidget(self.discount_input)
        discount_widget.setLayout(discount_layout)

        summary_layout.addWidget(discount_label, 1, 0)
        summary_layout.addWidget(discount_widget, 1, 1)
        summary_layout.addWidget(paid_label, 1, 2)
        summary_layout.addWidget(self.paid_amount, 1, 3)

        # إضافة تسمية الخصم المحسوب في صف منفصل
        summary_layout.addWidget(self.discount_calculated_label, 2, 1)

        summary_layout.addWidget(total_label, 3, 0)
        summary_layout.addWidget(self.total_value, 3, 1)
        summary_layout.addWidget(remaining_label, 3, 2)
        summary_layout.addWidget(self.remaining_value, 3, 3)

        summary_frame.setLayout(summary_layout)
        layout.addWidget(summary_frame)
        
        # أزرار التحكم النهائية
        buttons_layout = QHBoxLayout()
        
        save_print_btn = QPushButton("حفظ وطباعة")
        save_print_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 140px;
                padding: 10px;
            }
        """)
        save_print_btn.clicked.connect(self.save_and_print)
        
        save_btn = QPushButton("حفظ الفاتورة")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                min-width: 140px;
                padding: 10px;
            }
        """)
        save_btn.clicked.connect(self.save_invoice)

        # زر تعليق الفاتورة (مسودة)
        draft_btn = QPushButton("📝 تعليق الفاتورة")
        draft_btn.setStyleSheet("""
            QPushButton {
                background-color: #FFC107;
                color: #212529;
                min-width: 140px;
                padding: 10px;
                font-weight: bold;
            }
        """)
        draft_btn.clicked.connect(self.save_draft_invoice)
        draft_btn.setToolTip("حفظ الفاتورة كمسودة بدون تأثير على المخزون")

        # زر عرض المسودات
        drafts_btn = QPushButton("📋 المسودات")
        drafts_btn.setStyleSheet("""
            QPushButton {
                background-color: #17A2B8;
                min-width: 140px;
                padding: 10px;
            }
        """)
        drafts_btn.clicked.connect(self.show_drafts_dialog)
        drafts_btn.setToolTip("عرض وإدارة الفواتير المعلقة")

        clear_btn = QPushButton("فاتورة جديدة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 140px;
                padding: 10px;
            }
        """)
        clear_btn.clicked.connect(self.clear_invoice)

        buttons_layout.addWidget(clear_btn)
        buttons_layout.addWidget(drafts_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(draft_btn)
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(save_print_btn)
        
        layout.addLayout(buttons_layout)

        # إضافة اختصار كيبورد لإظهار/إخفاء المكسب
        self.profit_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        self.profit_shortcut.activated.connect(self.toggle_profit_visibility)

        # متغير لتتبع حالة المكسب
        self.profit_visible = False

        # متغير لتتبع المسودة الحالية
        self.current_draft_id = None

    def toggle_profit_visibility(self):
        """تبديل إظهار/إخفاء خانة المكسب السرية باستخدام Ctrl+T"""
        self.profit_visible = not self.profit_visible

        # إظهار/إخفاء خانة المكسب السرية
        self.profit_value.setVisible(self.profit_visible)

        # تحديث المكسب إذا كان مرئياً
        if self.profit_visible:
            self.update_total()

    def on_discount_type_changed(self, discount_type):
        """معالجة تغيير نوع الخصم"""
        if discount_type == "مبلغ":
            self.discount_input.setSuffix(" ج.م")
            self.discount_input.setMaximum(999999)
            self.discount_input.setToolTip("أدخل قيمة الخصم بالجنيه")
        else:  # نسبة %
            self.discount_input.setSuffix(" %")
            self.discount_input.setMaximum(100)
            self.discount_input.setToolTip("أدخل نسبة الخصم المئوية")

        # إعادة تعيين القيمة وتحديث المجاميع
        self.discount_input.setValue(0)
        self.update_total()

    def generate_invoice_number(self):
        with Session(self.engine) as session:
            last_invoice = session.query(Transaction).filter(
                Transaction.type == TransactionType.SALE
            ).order_by(Transaction.id.desc()).first()
            
            if last_invoice:
                new_number = last_invoice.id + 1
            else:
                new_number = 1
                
            self.invoice_number.setText(f"رقم الفاتورة: {new_number:06d}")

    def setup_autocomplete(self):
        """إعداد القائمة المنسدلة التلقائية للبحث"""
        from PyQt5.QtWidgets import QCompleter
        from PyQt5.QtCore import QStringListModel

        # إنشاء قائمة بجميع المنتجات
        self.update_autocomplete_list()

        # إنشاء QCompleter
        self.completer = QCompleter()
        self.completer.setModel(QStringListModel())
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)
        self.completer.setMaxVisibleItems(15)  # زيادة عدد العناصر المرئية

        # تخصيص مظهر القائمة المنسدلة
        self.completer.popup().setStyleSheet("""
            QListView {
                background-color: white;
                border: 2px solid #3498DB;
                border-radius: 8px;
                font-size: 16px;
                padding: 8px;
                selection-background-color: #3498DB;
                selection-color: white;
                min-height: 400px;
                max-height: 500px;
                min-width: 450px;
            }
            QListView::item {
                padding: 12px 15px;
                border-bottom: 1px solid #ECF0F1;
                min-height: 25px;
            }
            QListView::item:hover {
                background-color: #EBF3FD;
                border-radius: 4px;
            }
            QListView::item:selected {
                background-color: #3498DB;
                color: white;
                border-radius: 4px;
            }
        """)

        # تحسين حجم القائمة المنسدلة تلقائياً
        self.completer.popup().setMinimumSize(450, 400)
        self.completer.popup().setMaximumSize(600, 500)

        # ربط القائمة بحقل البحث
        self.quick_search.setCompleter(self.completer)

        # ربط اختيار عنصر من القائمة
        self.completer.activated.connect(self.on_autocomplete_selected)

    def update_autocomplete_list(self):
        """تحديث قائمة الإكمال التلقائي"""
        try:
            with Session(self.engine) as session:
                products = session.query(Product).filter(Product.is_active == True).all()

                # إنشاء قائمة بالمنتجات مع معلومات متعددة
                product_list = []
                self.product_map = {}  # خريطة لربط النص بالمنتج

                for product in products:
                    # إضافة الاسم فقط بدون رموز أو أكواد
                    name_entry = product.name
                    product_list.append(name_entry)
                    self.product_map[name_entry] = product

                # تحديث نموذج البيانات
                if hasattr(self, 'completer'):
                    self.completer.model().setStringList(product_list)

        except Exception as e:
            print(f"خطأ في تحديث قائمة الإكمال التلقائي: {e}")

    def on_search_text_changed(self, text):
        """معالجة تغيير نص البحث"""
        if len(text) >= 1:
            # تحديث قائمة الإكمال التلقائي بناءً على النص المدخل
            self.filter_autocomplete(text)

    def filter_autocomplete(self, text):
        """تصفية قائمة الإكمال التلقائي"""
        try:
            with Session(self.engine) as session:
                # البحث في الاسم والكود والباركود
                products = session.query(Product).filter(
                    (Product.name.ilike(f"%{text}%")) |
                    (Product.code.ilike(f"%{text}%")) |
                    (Product.barcode.ilike(f"%{text}%"))
                ).filter(Product.is_active == True).limit(10).all()

                # إنشاء قائمة مصفاة
                filtered_list = []
                self.product_map = {}

                for product in products:
                    # إضافة الاسم فقط بدون رموز أو أكواد أو أسعار
                    name_entry = product.name
                    filtered_list.append(name_entry)
                    self.product_map[name_entry] = product

                # تحديث القائمة
                self.completer.model().setStringList(filtered_list)

                # تحسين حجم القائمة بناءً على عدد النتائج
                self.adjust_popup_size(len(filtered_list))

        except Exception as e:
            print(f"خطأ في تصفية قائمة الإكمال التلقائي: {e}")

    def adjust_popup_size(self, item_count):
        """تحسين حجم القائمة المنسدلة بناءً على عدد العناصر"""
        try:
            popup = self.completer.popup()

            # حساب الارتفاع المناسب
            item_height = 37  # ارتفاع كل عنصر (12px padding + 25px min-height)
            base_height = 50  # ارتفاع أساسي للحدود والحشو

            # حساب الارتفاع المطلوب
            if item_count == 0:
                calculated_height = 100  # حد أدنى عند عدم وجود نتائج
            elif item_count <= 5:
                calculated_height = base_height + (item_count * item_height)
            elif item_count <= 10:
                calculated_height = base_height + (item_count * item_height)
            else:
                calculated_height = 500  # الحد الأقصى

            # تطبيق الحدود
            min_height = 150
            max_height = 500
            final_height = max(min_height, min(calculated_height, max_height))

            # حساب العرض المناسب
            if item_count > 0:
                # العرض يعتمد على طول أطول نص
                min_width = 450
                max_width = 700
                calculated_width = min_width + (item_count * 5)  # زيادة تدريجية
                final_width = min(calculated_width, max_width)
            else:
                final_width = 450

            # تطبيق الأحجام الجديدة
            popup.setMinimumSize(final_width, final_height)
            popup.setMaximumSize(final_width + 50, final_height + 50)

            # تحديث عدد العناصر المرئية
            visible_items = min(15, max(5, item_count))
            self.completer.setMaxVisibleItems(visible_items)

        except Exception as e:
            print(f"خطأ في تحسين حجم القائمة المنسدلة: {e}")

    def on_search_enter(self):
        """معالجة الضغط على Enter في حقل البحث"""
        search_text = self.quick_search.text().strip()
        if search_text:
            self.search_and_add_product(search_text)

    def on_autocomplete_selected(self, text):
        """معالجة اختيار عنصر من قائمة الإكمال التلقائي"""
        if text in self.product_map:
            product = self.product_map[text]
            self.add_product_to_table(product, 1, product.sale_price)
            self.quick_search.clear()

            QMessageBox.information(
                self,
                "✅ تم الإضافة",
                f"تم إضافة '{product.name}' للفاتورة بنجاح!"
            )

    def search_and_add_product(self, search_text):
        """البحث عن منتج وإضافته للفاتورة"""
        try:
            with Session(self.engine) as session:
                # البحث بالاسم أو الكود أو الباركود الأساسي
                product = session.query(Product).filter(
                    (Product.name.ilike(f"%{search_text}%")) |
                    (Product.code == search_text) |
                    (Product.barcode == search_text)
                ).filter(Product.is_active == True).first()

                # إذا لم يوجد، ابحث في الباركودات المتعددة
                if not product:
                    barcode_record = session.query(ProductBarcode).filter(
                        ProductBarcode.barcode == search_text
                    ).first()
                    if barcode_record:
                        product = barcode_record.product

                if product:
                    # التحقق من توفر المنتج
                    if product.quantity <= 0:
                        QMessageBox.warning(
                            self,
                            "⚠️ نفد المخزون",
                            f"المنتج '{product.name}' غير متوفر في المخزون!"
                        )
                        return

                    # إضافة المنتج للفاتورة
                    self.add_product_to_table(product, 1, product.sale_price)
                    self.quick_search.clear()

                    QMessageBox.information(
                        self,
                        "✅ تم الإضافة",
                        f"تم إضافة '{product.name}' للفاتورة بنجاح!"
                    )
                else:
                    QMessageBox.warning(
                        self,
                        "❌ منتج غير موجود",
                        f"لم يتم العثور على منتج يطابق: '{search_text}'"
                    )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء البحث:\n{str(e)}"
            )

    def scan_barcode_camera(self):
        """مسح الباركود بالكاميرا لإضافة منتج للفاتورة"""
        try:
            from .barcode_scanner import BarcodeScannerDialog

            scanner = BarcodeScannerDialog(self)
            scanner.barcode_detected.connect(self.on_barcode_detected)
            scanner.exec_()

        except ImportError as e:
            QMessageBox.warning(
                self,
                "مكتبات مفقودة",
                "لاستخدام قارئ الباركود بالكاميرا، يجب تثبيت المكتبات التالية:\n"
                "pip install opencv-python pyzbar\n\n"
                f"تفاصيل الخطأ: {str(e)}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح قارئ الباركود:\n{str(e)}"
            )

    def scan_barcode_device(self):
        """مسح الباركود بالجهاز المحمول لإضافة منتج للفاتورة"""
        try:
            from .barcode_device_scanner import BarcodeDeviceScannerDialog

            scanner = BarcodeDeviceScannerDialog(self)
            scanner.barcode_detected.connect(self.on_barcode_detected)
            scanner.exec_()

        except ImportError as e:
            # إذا لم تكن المكتبة موجودة، استخدم نافذة بسيطة
            self.show_barcode_input_dialog()
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح قارئ الباركود:\n{str(e)}"
            )

    def show_barcode_input_dialog(self):
        """نافذة بسيطة لإدخال الباركود من الجهاز المحمول"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QHBoxLayout

        dialog = QDialog(self)
        dialog.setWindowTitle("مسح الباركود - إضافة للفاتورة")
        dialog.setObjectName("large_dialog")  # لتطبيق الأنماط الكبيرة
        dialog.setModal(True)

        # إضافة أزرار التحكم في النافذة (تكبير/تصغير)
        dialog.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)

        dialog.setMinimumSize(900, 700)
        dialog.resize(1000, 750)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
                color: #2C3E50;
                padding: 25px;
                border-radius: 15px;
            }
            QLabel {
                color: #2C3E50;
                font-size: 18px;
                padding: 8px;
                word-wrap: true;
                line-height: 1.5;
            }
            QLineEdit {
                background-color: white;
                color: #2C3E50;
                font-size: 20px;
                padding: 15px;
                border: 3px solid #3498DB;
                border-radius: 10px;
                min-height: 50px;
                min-width: 600px;
            }
            QLineEdit:focus {
                border-color: #2980B9;
                background-color: #EBF5FB;
            }
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                min-width: 180px;
                min-height: 55px;
                margin: 8px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
            QPushButton:pressed {
                background-color: #21618C;
            }
        """)

        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # عنوان
        title = QLabel("🛒 إضافة منتج للفاتورة")
        title.setStyleSheet("""
            font-size: 28px;
            font-weight: bold;
            text-align: center;
            margin: 25px 0;
            color: #2C3E50;
            padding: 15px;
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تعليمات
        instructions = QLabel("""
        📋 إضافة منتجات متعددة بالباركود:

        1️⃣ تأكد من توصيل جهاز الباركود
        2️⃣ امسح الباركود الأول بالجهاز المحمول
        3️⃣ اضغط "إضافة والمتابعة" أو Enter
        4️⃣ امسح الباركود التالي مباشرة
        5️⃣ كرر العملية لجميع المنتجات
        6️⃣ اضغط "إنهاء وإغلاق" عند الانتهاء
        """)
        instructions.setStyleSheet("""
            background-color: #EBF3FD;
            padding: 20px;
            border-radius: 10px;
            font-size: 16px;
            color: #2C3E50;
            border: 2px solid #3498DB;
            line-height: 1.6;
            margin: 10px 0;
        """)
        instructions.setWordWrap(True)
        layout.addWidget(instructions)

        # حقل إدخال الباركود
        barcode_label = QLabel("📊 الباركود:")
        barcode_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-top: 20px;")
        layout.addWidget(barcode_label)

        barcode_input = QLineEdit()
        barcode_input.setPlaceholderText("امسح الباركود هنا... (النافذة ستبقى مفتوحة لإضافة منتجات متعددة)")
        barcode_input.setFocus()  # التركيز على الحقل
        layout.addWidget(barcode_input)

        # عداد المنتجات المضافة
        products_counter = QLabel("📊 المنتجات المضافة: 0")
        products_counter.setStyleSheet("""
            background-color: #2C3E50;
            color: #F39C12;
            font-size: 16px;
            font-weight: bold;
            padding: 10px;
            border: 2px solid #F39C12;
            border-radius: 8px;
            text-align: center;
        """)
        products_counter.setAlignment(Qt.AlignCenter)
        layout.addWidget(products_counter)

        # أزرار - تخطيط محسن
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 10px;
                padding: 20px;
                margin: 15px 0;
            }
        """)
        buttons_layout = QHBoxLayout()
        buttons_frame.setLayout(buttons_layout)

        add_btn = QPushButton("➕ إضافة والمتابعة")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                min-width: 200px;
                min-height: 55px;
                margin: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        finish_btn = QPushButton("✅ إنهاء وإغلاق")
        finish_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                min-width: 200px;
                min-height: 55px;
                margin: 8px;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
        """)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 10px;
                min-width: 200px;
                min-height: 55px;
                margin: 8px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)

        buttons_layout.addStretch()
        buttons_layout.addWidget(add_btn)
        buttons_layout.addWidget(finish_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

        # متغير لتتبع عدد المنتجات المضافة
        added_products_count = [0]  # استخدام قائمة للتمكن من التعديل داخل الدالة

        # ربط الأزرار
        def add_product():
            barcode = barcode_input.text().strip()
            if barcode:
                # حفظ العدد الحالي للمقارنة
                old_count = len(self.items)
                self.on_barcode_detected(barcode)
                # التحقق من إضافة منتج جديد
                new_count = len(self.items)
                if new_count > old_count:
                    added_products_count[0] += 1
                    products_counter.setText(f"📊 المنتجات المضافة: {added_products_count[0]}")

                # مسح الحقل للمنتج التالي بدلاً من إغلاق النافذة
                barcode_input.clear()
                barcode_input.setFocus()
            else:
                QMessageBox.warning(dialog, "تحذير", "يرجى إدخال الباركود أولاً!")

        def finish():
            dialog.accept()

        def cancel():
            dialog.reject()

        # ربط Enter بزر الإضافة
        barcode_input.returnPressed.connect(add_product)
        add_btn.clicked.connect(add_product)
        finish_btn.clicked.connect(finish)
        cancel_btn.clicked.connect(cancel)

        dialog.exec_()

    def on_barcode_detected(self, barcode):
        """معالجة الباركود المكتشف وإضافة المنتج للفاتورة"""
        try:
            with Session(self.engine) as session:
                # البحث في الباركود الأساسي أولاً
                product = session.query(Product).filter(Product.barcode == barcode).first()

                # إذا لم يوجد، ابحث في الباركودات المتعددة
                if not product:
                    barcode_record = session.query(ProductBarcode).filter(
                        ProductBarcode.barcode == barcode
                    ).first()
                    if barcode_record:
                        product = barcode_record.product

                if product:
                    # التحقق من توفر المنتج في المخزون
                    if product.quantity <= 0:
                        QMessageBox.warning(
                            self,
                            "⚠️ نفد المخزون",
                            f"المنتج '{product.name}' غير متوفر في المخزون!"
                        )
                        return

                    # التحقق من وجود المنتج في الفاتورة مسبقاً
                    existing_item = None
                    for i, item in enumerate(self.items):
                        if item['product_id'] == product.id:
                            existing_item = i
                            break

                    if existing_item is not None:
                        # المنتج موجود - زيادة الكمية
                        current_quantity = self.items[existing_item]['quantity']
                        new_quantity = current_quantity + 1

                        if new_quantity > product.quantity:
                            QMessageBox.warning(
                                self,
                                "⚠️ كمية غير متوفرة",
                                f"الكمية المطلوبة ({new_quantity}) أكبر من المتوفر ({product.quantity})"
                            )
                            return

                        # تحديث الكمية في الجدول والقائمة
                        self.items[existing_item]['quantity'] = new_quantity

                        # تحديث الكمية مع التنسيق
                        quantity_item = QTableWidgetItem(f"{new_quantity:,}")
                        quantity_item.setTextAlignment(Qt.AlignCenter)
                        quantity_item.setBackground(QColor("#FFE4B5"))  # لون تمييز ذهبي
                        self.products_table.setItem(existing_item, 1, quantity_item)

                        # تحديث الإجمالي مع التنسيق
                        new_total = self.items[existing_item]['price'] * new_quantity
                        total_item = QTableWidgetItem(f"{new_total:,.0f} ج.م")
                        total_item.setTextAlignment(Qt.AlignCenter)
                        total_item.setBackground(QColor("#FFE4B5"))  # لون تمييز ذهبي
                        # تعيين لون النص والخط العريض
                        total_item.setForeground(QColor("#27AE60"))
                        font = total_item.font()
                        font.setBold(True)
                        total_item.setFont(font)
                        self.products_table.setItem(existing_item, 3, total_item)

                        # تمييز الصف المحدث
                        for col in range(self.products_table.columnCount()):
                            item = self.products_table.item(existing_item, col)
                            if item:
                                item.setBackground(QColor("#FFE4B5"))  # لون تمييز ذهبي

                        QMessageBox.information(
                            self,
                            "✅ تم التحديث",
                            f"تم زيادة كمية '{product.name}' إلى {new_quantity}"
                        )
                    else:
                        # منتج جديد - إضافة للفاتورة
                        self.add_product_to_table(product, 1, product.sale_price)

                        QMessageBox.information(
                            self,
                            "✅ تم الإضافة",
                            f"تم إضافة '{product.name}' للفاتورة بنجاح!"
                        )

                    # تحديث المجاميع
                    self.update_total()

                else:
                    # المنتج غير موجود
                    reply = QMessageBox.question(
                        self,
                        "❓ منتج غير موجود",
                        f"لم يتم العثور على منتج بالباركود:\n{barcode}\n\n"
                        "هل تريد إضافة منتج جديد بهذا الباركود؟",
                        QMessageBox.Yes | QMessageBox.No
                    )

                    if reply == QMessageBox.Yes:
                        # فتح نافذة إضافة منتج جديد
                        from .inventory import ProductDialog
                        dialog = ProductDialog(self.engine, parent=self)
                        dialog.barcode_input.setText(barcode)
                        if dialog.exec_() == QDialog.Accepted:
                            QMessageBox.information(
                                self,
                                "✅ تم إضافة المنتج",
                                "تم إضافة المنتج الجديد بنجاح!\nيمكنك الآن مسح الباركود مرة أخرى لإضافته للفاتورة."
                            )

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء معالجة الباركود:\n{str(e)}"
            )

    def add_product_to_invoice(self, product):
        dialog = ProductSelectionDialog(self.engine)
        dialog.quantity_spin.setValue(1)
        dialog.price_spin.setValue(product.sale_price)
        
        if dialog.exec_() == QDialog.Accepted:
            quantity = dialog.quantity_spin.value()
            price = dialog.price_spin.value()
            
            if product.quantity < quantity:
                QMessageBox.warning(self, "خطأ", "الكمية المطلوبة غير متوفرة في المخزون")
                return
                
            self.add_product_to_table(product, quantity, price)

    def add_product_to_table(self, product, quantity, price):
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)
        
        # إضافة المنتج للجدول مع تنسيق خلفية الصف حسب الوضع
        bg_color = self.get_row_colors(row)
        
        # إضافة البيانات للجدول مع تنسيق محسن
        product_item = self.create_table_item(f"🛍️ {product.name}", bg_color)
        product_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.products_table.setItem(row, 0, product_item)

        quantity_item = self.create_table_item(f"{quantity:,}", bg_color)
        quantity_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 1, quantity_item)

        price_item = self.create_table_item(f"{price:,.0f} ج.م", bg_color)
        price_item.setTextAlignment(Qt.AlignCenter)
        self.products_table.setItem(row, 2, price_item)

        total = price * quantity
        total_item = self.create_table_item(f"{total:,.0f} ج.م", bg_color)
        total_item.setTextAlignment(Qt.AlignCenter)
        # تعيين لون النص للسعر النهائي
        total_item.setForeground(QColor("#27AE60"))
        font = total_item.font()
        font.setBold(True)
        total_item.setFont(font)
        self.products_table.setItem(row, 3, total_item)

        # زر الحذف المحسن مع تنسيق أفضل
        delete_btn = QPushButton("🗑️")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                border: none;
                color: white;
                padding: 6px 8px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 16px;
                min-width: 35px;
                max-width: 35px;
                min-height: 30px;
                max-height: 30px;
            }
            QPushButton:hover {
                background-color: #C82333;
                transform: scale(1.1);
            }
            QPushButton:pressed {
                background-color: #A71E2A;
            }
        """)
        delete_btn.setToolTip("حذف هذا المنتج من الفاتورة")
        # ربط الزر بمعرف المنتج بدلاً من رقم الصف
        delete_btn.setProperty("product_id", product.id)
        delete_btn.clicked.connect(self.handle_delete_by_product_id)

        # إنشاء widget حاوي للزر لتحسين التنسيق
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.addWidget(delete_btn)
        button_layout.setAlignment(Qt.AlignCenter)
        button_layout.setContentsMargins(5, 2, 5, 2)

        self.products_table.setCellWidget(row, 4, button_widget)
        
        self.items.append({
            'product_id': product.id,
            'quantity': quantity,
            'price': price
        })
        
        self.update_total()

    def create_table_item(self, text, bg_color):
        item = QTableWidgetItem(text)
        item.setBackground(bg_color)
        return item

    def handle_delete_by_product_id(self):
        """معالج الحذف باستخدام معرف المنتج"""
        try:
            # الحصول على الزر الذي تم الضغط عليه
            button = self.sender()
            product_id = button.property("product_id")

            if product_id is None:
                QMessageBox.warning(
                    self,
                    "⚠️ خطأ",
                    "لا يمكن تحديد المنتج المراد حذفه!"
                )
                return

            # البحث عن المنتج في القائمة والجدول
            item_index = -1
            table_row = -1

            # البحث في قائمة المنتجات
            for i, item in enumerate(self.items):
                if item['product_id'] == product_id:
                    item_index = i
                    break

            # البحث في الجدول
            for row in range(self.products_table.rowCount()):
                cell_widget = self.products_table.cellWidget(row, 4)
                if cell_widget:
                    # البحث عن الزر داخل الـ widget الحاوي
                    layout = cell_widget.layout()
                    if layout and layout.itemAt(0):
                        widget_button = layout.itemAt(0).widget()
                        if widget_button == button:
                            table_row = row
                            break

            if item_index >= 0 and table_row >= 0:
                self.remove_product_by_indices(item_index, table_row, product_id)
            else:
                QMessageBox.warning(
                    self,
                    "⚠️ خطأ",
                    "لا يمكن العثور على المنتج المراد حذفه!"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء معالجة الحذف:\n{str(e)}"
            )

    def handle_delete_click(self):
        """معالج النقر على زر الحذف - للتوافق مع الكود القديم"""
        self.handle_delete_by_product_id()

    def on_item_changed(self, item):
        """معالجة تغيير البيانات في الجدول لتحديث السعر النهائي تلقائياً"""
        if not item:
            return

        row = item.row()
        col = item.column()

        # التحقق من أن التغيير في عمود الكمية (1) أو السعر (2)
        if col not in [1, 2]:
            return

        try:
            # منع التحديث المتكرر
            self.products_table.blockSignals(True)

            # الحصول على القيم الحالية
            quantity_item = self.products_table.item(row, 1)
            price_item = self.products_table.item(row, 2)

            if not quantity_item or not price_item:
                return

            # استخراج الأرقام من النصوص
            quantity_text = quantity_item.text().replace(',', '')
            price_text = price_item.text().replace(' ج.م', '').replace(',', '')

            try:
                quantity = float(quantity_text)
                price = float(price_text)

                # التحقق من صحة القيم
                if quantity <= 0 or price <= 0:
                    QMessageBox.warning(
                        self,
                        "⚠️ قيمة غير صحيحة",
                        "يجب أن تكون الكمية والسعر أكبر من صفر!"
                    )
                    # إعادة القيمة السابقة
                    if col == 1:  # الكمية
                        if row < len(self.items):
                            old_quantity = self.items[row]['quantity']
                            quantity_item.setText(f"{old_quantity:,}")
                    else:  # السعر
                        if row < len(self.items):
                            old_price = self.items[row]['price']
                            price_item.setText(format_currency(old_price))
                    return

                # التحقق من توفر الكمية في المخزون
                if col == 1:  # تغيير الكمية
                    if row < len(self.items):
                        product_id = self.items[row]['product_id']
                        with Session(self.engine) as session:
                            product = session.query(Product).get(product_id)
                            if product and quantity > product.quantity:
                                QMessageBox.warning(
                                    self,
                                    "⚠️ كمية غير متوفرة",
                                    f"الكمية المطلوبة ({quantity:,}) أكبر من المتوفر ({product.quantity:,})"
                                )
                                # إعادة القيمة السابقة
                                old_quantity = self.items[row]['quantity']
                                quantity_item.setText(f"{old_quantity:,}")
                                return

                # تحديث البيانات في القائمة
                if row < len(self.items):
                    if col == 1:  # الكمية
                        # حفظ الكمية القديمة للمقارنة
                        old_item_quantity = self.items[row]['quantity']
                        quantity_increase = quantity - old_item_quantity

                        self.items[row]['quantity'] = quantity
                        # تحديث تنسيق الكمية
                        quantity_item.setText(f"{quantity:,}")
                        quantity_item.setTextAlignment(Qt.AlignCenter)

                        # فحص المخزون إذا زادت الكمية المطلوبة
                        if quantity_increase > 0:
                            product_id = self.items[row]['product_id']
                            with Session(self.engine) as session:
                                product = session.query(Product).get(product_id)
                                if product:
                                    # محاكاة تأثير البيع على المخزون
                                    simulated_new_quantity = product.quantity - quantity_increase
                                    if (product.min_quantity > 0 and
                                        simulated_new_quantity <= product.min_quantity and
                                        product.quantity > product.min_quantity):
                                        # إنشاء كائن مؤقت للمحاكاة
                                        temp_product = type('Product', (), {
                                            'name': product.name,
                                            'quantity': simulated_new_quantity,
                                            'min_quantity': product.min_quantity
                                        })()
                                        self.show_low_stock_popup(temp_product)
                    else:  # السعر
                        # التحقق من أن السعر مربح قبل التحديث
                        product_id = self.items[row]['product_id']
                        with Session(self.engine) as session:
                            product = session.query(Product).get(product_id)
                            if product:
                                # فحص الحد الأدنى للبيع أولاً
                                min_sale_price = getattr(product, 'min_sale_price', 0) or 0
                                if min_sale_price > 0 and price < min_sale_price:
                                    QMessageBox.warning(
                                        self,
                                        "🚫 تحذير: أقل من الحد الأدنى للبيع",
                                        f"لا يمكن البيع بهذا السعر!\n\n"
                                        f"📦 المنتج: {product.name}\n"
                                        f"🔴 السعر المطلوب: {price:,.0f} ج.م\n"
                                        f"⚠️ الحد الأدنى للبيع: {min_sale_price:,.0f} ج.م\n\n"
                                        f"💡 يجب أن يكون السعر أكبر من أو يساوي الحد الأدنى المحدد"
                                    )
                                    # إعادة السعر القديم
                                    old_price = self.items[row]['price']
                                    price_item.setText(format_currency(old_price))
                                    return

                                # فحص أن السعر أكبر من سعر الشراء
                                if price <= product.purchase_price:
                                    QMessageBox.warning(
                                        self,
                                        "⚠️ تحذير: سعر غير مربح",
                                        f"لا يمكن تحديث السعر!\n\n"
                                        f"📦 المنتج: {product.name}\n"
                                        f"💰 سعر الشراء: {product.purchase_price:,.0f} ج.م\n"
                                        f"🔴 السعر المطلوب: {price:,.0f} ج.م\n\n"
                                        f"⚠️ سعر البيع يجب أن يكون أكبر من سعر الشراء لضمان الربحية!\n"
                                        f"💡 الحد الأدنى المسموح: {product.purchase_price + 1:,.0f} ج.م"
                                    )
                                    # إعادة السعر القديم
                                    old_price = self.items[row]['price']
                                    price_item.setText(format_currency(old_price))
                                    return

                        # التحقق من تغيير السعر وعرض نافذة التأكيد
                        old_price = self.items[row]['price']
                        if price != old_price:
                            self.ask_update_product_price(row, price, old_price)

                        self.items[row]['price'] = price
                        # تحديث تنسيق السعر
                        price_item.setText(format_currency(price))
                        price_item.setTextAlignment(Qt.AlignCenter)

                # حساب وتحديث السعر النهائي
                new_total = quantity * price
                total_item = self.products_table.item(row, 3)
                if total_item:
                    total_item.setText(format_currency(new_total))
                    total_item.setTextAlignment(Qt.AlignCenter)
                    # تعيين لون النص والخط العريض
                    total_item.setForeground(QColor("#27AE60"))
                    font = total_item.font()
                    font.setBold(True)
                    total_item.setFont(font)

                    # تمييز الصف المحدث
                    bg_color = QColor("#0F172A")  # لون داكن عصري للتمييز
                    for c in range(self.products_table.columnCount() - 1):  # عدا عمود الحذف
                        cell_item = self.products_table.item(row, c)
                        if cell_item:
                            cell_item.setBackground(bg_color)

                # تحديث المجاميع
                self.update_total()

            except ValueError:
                QMessageBox.warning(
                    self,
                    "⚠️ قيمة غير صحيحة",
                    "يرجى إدخال أرقام صحيحة فقط!"
                )
                # إعادة القيمة السابقة
                if col == 1:  # الكمية
                    if row < len(self.items):
                        old_quantity = self.items[row]['quantity']
                        quantity_item.setText(f"{old_quantity:,}")
                else:  # السعر
                    if row < len(self.items):
                        old_price = self.items[row]['price']
                        price_item.setText(format_currency(old_price))

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}"
            )
        finally:
            # إعادة تفعيل الإشارات
            self.products_table.blockSignals(False)
                
    def remove_product_by_indices(self, item_index, table_row, product_id):
        """حذف منتج باستخدام الفهارس المحددة"""
        try:
            # التحقق من صحة الفهارس
            if (0 <= item_index < len(self.items) and
                0 <= table_row < self.products_table.rowCount() and
                self.items[item_index]['product_id'] == product_id):

                # الحصول على معلومات المنتج قبل الحذف
                product_name = self.products_table.item(table_row, 0).text() if self.products_table.item(table_row, 0) else "منتج غير معروف"
                clean_product_name = product_name.replace('🛍️ ', '')

                # رسالة تأكيد الحذف
                reply = QMessageBox.question(
                    self,
                    "🗑️ تأكيد الحذف",
                    f"هل أنت متأكد من حذف المنتج:\n'{clean_product_name}'\nمن الفاتورة؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No  # الخيار الافتراضي هو "لا"
                )

                # إذا أكد المستخدم الحذف
                if reply == QMessageBox.Yes:
                    # حذف المنتج من القائمة والجدول
                    self.items.pop(item_index)
                    self.products_table.removeRow(table_row)

                    # تحديث المجاميع
                    self.update_total()

                    QMessageBox.information(
                        self,
                        "✅ تم الحذف",
                        f"تم حذف '{clean_product_name}' من الفاتورة بنجاح!"
                    )
                # إذا ألغى المستخدم العملية، لا نفعل شيئاً

            else:
                QMessageBox.warning(
                    self,
                    "⚠️ خطأ",
                    "فهارس المنتج غير صحيحة!"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء حذف المنتج:\n{str(e)}"
            )

    def remove_product_safe(self, row):
        """حذف منتج من الفاتورة بطريقة آمنة - للتوافق مع الكود القديم"""
        try:
            # التحقق من صحة الفهرس
            if 0 <= row < len(self.items) and 0 <= row < self.products_table.rowCount():
                product_id = self.items[row]['product_id']
                self.remove_product_by_indices(row, row, product_id)
            else:
                QMessageBox.warning(
                    self,
                    "⚠️ خطأ",
                    "لا يمكن حذف هذا المنتج!"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء حذف المنتج:\n{str(e)}"
            )

    def remove_product(self, row):
        """دالة الحذف القديمة - للتوافق مع الكود القديم"""
        self.remove_product_safe(row)

    def update_total(self):
        # حساب المجموع الفرعي
        subtotal = sum(item['price'] * item['quantity'] for item in self.items)

        # حساب المكسب (الفرق بين سعر البيع وسعر الشراء)
        total_profit = 0
        if hasattr(self, 'profit_visible') and self.profit_visible:
            try:
                with Session(self.engine) as session:
                    for item in self.items:
                        product = session.query(Product).get(item['product_id'])
                        if product:
                            # المكسب = (سعر البيع - سعر الشراء) × الكمية
                            profit_per_unit = item['price'] - product.purchase_price
                            total_profit += profit_per_unit * item['quantity']
            except Exception as e:
                print(f"خطأ في حساب المكسب: {e}")
                total_profit = 0

        # حساب الخصم حسب النوع المختار
        discount_value = self.discount_input.value() if hasattr(self, 'discount_input') else 0
        discount_type = self.discount_type.currentText() if hasattr(self, 'discount_type') else "مبلغ"

        if discount_type == "نسبة %":
            # تحويل النسبة إلى مبلغ
            discount = subtotal * (discount_value / 100)
            # عرض المبلغ المحسوب
            if hasattr(self, 'discount_calculated_label'):
                self.discount_calculated_label.setText(f"= {format_currency(discount)}")
        else:
            # مبلغ مباشر
            discount = discount_value
            # مسح تسمية المبلغ المحسوب
            if hasattr(self, 'discount_calculated_label'):
                self.discount_calculated_label.setText("")

        # حساب الإجمالي النهائي (بدون ضريبة)
        total = subtotal - discount

        # التأكد من أن الإجمالي لا يقل عن صفر
        if total < 0:
            total = 0

        # حساب المبلغ المتبقي
        paid = self.paid_amount.value() if hasattr(self, 'paid_amount') else 0
        remaining = total - paid

        # التأكد من أن المتبقي لا يقل عن صفر
        if remaining < 0:
            remaining = 0

        # تحديث العرض
        self.subtotal_value.setText(format_currency(subtotal))
        self.total_value.setText(format_currency(total))

        # تحديث المكسب السري إذا كان مرئياً
        if hasattr(self, 'profit_value') and hasattr(self, 'profit_visible') and self.profit_visible:
            # خصم قيمة الخصم من المكسب
            final_profit = total_profit - discount

            # عرض الرقم فقط بدون كلمة "ريال"
            self.profit_value.setText(f"{final_profit:,.0f}")

            # تغيير لون المكسب حسب القيمة النهائية (بشكل سري)
            if final_profit > 0:
                self.profit_value.setStyleSheet("""
                    font-size: 14px;
                    color: rgba(144, 238, 144, 0.9);
                    background-color: rgba(144, 238, 144, 0.15);
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-weight: bold;
                """)
            elif final_profit < 0:
                self.profit_value.setStyleSheet("""
                    font-size: 14px;
                    color: rgba(255, 182, 193, 0.9);
                    background-color: rgba(255, 182, 193, 0.15);
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-weight: bold;
                """)
            else:
                self.profit_value.setStyleSheet("""
                    font-size: 14px;
                    color: rgba(255, 255, 255, 0.6);
                    background-color: rgba(255, 255, 255, 0.1);
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-weight: bold;
                """)

        # تحديث المبلغ المتبقي إذا كان موجوداً
        if hasattr(self, 'remaining_value'):
            self.remaining_value.setText(format_currency(remaining))

            # تغيير لون المتبقي حسب الحالة - ألوان عصرية
            if remaining == 0:
                self.remaining_value.setStyleSheet("color: #10B981; font-size: 23px; font-weight: bold; background-color: #0F172A; padding: 8px; border-radius: 5px;")
            else:
                self.remaining_value.setStyleSheet("color: #EF4444; font-size: 23px; font-weight: bold; background-color: #1E293B; padding: 8px; border-radius: 5px;")

        # تحديد الحد الأقصى للمبلغ المدفوع
        if hasattr(self, 'paid_amount'):
            self.paid_amount.setMaximum(total)
        
    def save_invoice(self):
        if not self.items:
            QMessageBox.warning(self, "خطأ", "لا يوجد منتجات في الفاتورة")
            return None

        customer_id = self.customer_combo.currentData()

        # السماح بالفواتير بدون عميل (فواتير سريعة)
        customer_name = "عميل نقدي"  # اسم افتراضي للفواتير السريعة
        if customer_id is not None:
            # إذا تم اختيار عميل، نتحقق من وجوده
            try:
                with Session(self.engine) as session:
                    customer = session.query(Customer).get(customer_id)
                    if customer:
                        customer_name = customer.name
                    else:
                        QMessageBox.warning(self, "خطأ", "العميل المحدد غير موجود في قاعدة البيانات")
                        return
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"خطأ في التحقق من العميل: {str(e)}")
                return

        subtotal = sum(item['price'] * item['quantity'] for item in self.items)

        # حساب الخصم حسب النوع المختار (نفس المنطق من update_total)
        discount_value = self.discount_input.value() if hasattr(self, 'discount_input') else 0
        discount_type = self.discount_type.currentText() if hasattr(self, 'discount_type') else "مبلغ"

        if discount_type == "نسبة %":
            # تحويل النسبة إلى مبلغ للحفظ في قاعدة البيانات
            discount = subtotal * (discount_value / 100)
        else:
            # مبلغ مباشر
            discount = discount_value

        total_amount = subtotal - discount
        paid_amount = self.paid_amount.value()

        # التأكد من أن الإجمالي لا يقل عن صفر
        if total_amount < 0:
            total_amount = 0

        try:
            with Session(self.engine) as session:
                # إنشاء الفاتورة (مع أو بدون عميل)
                transaction = Transaction(
                    type=TransactionType.SALE,
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    discount=discount,  # حفظ قيمة الخصم
                    customer_id=customer_id  # يمكن أن يكون None للفواتير السريعة
                )
                session.add(transaction)

                # إضافة المنتجات للفاتورة
                for item in self.items:
                    transaction_item = TransactionItem(
                        transaction=transaction,
                        product_id=item['product_id'],
                        quantity=item['quantity'],
                        price=item['price']
                    )
                    session.add(transaction_item)

                    # تحديث المخزون
                    product = session.query(Product).get(item['product_id'])
                    if product:
                        old_quantity = product.quantity
                        product.quantity -= item['quantity']

                        # فحص فوري للمخزون المنخفض بعد البيع
                        self.check_low_stock_alert(product, old_quantity)

                # تحديث رصيد العميل (فقط إذا كان هناك عميل ومبلغ متبقي)
                remaining_amount = total_amount - paid_amount
                if customer_id is not None and remaining_amount > 0:
                    customer = session.query(Customer).get(customer_id)
                    if customer:
                        customer.balance += remaining_amount

                session.commit()

                # حفظ ID الفاتورة للإرجاع
                invoice_id = transaction.id

                # رسالة نجاح مفصلة
                invoice_type = "💳 فاتورة نقدية سريعة" if customer_id is None else "📋 فاتورة عميل"
                payment_status = "✅ مدفوعة بالكامل" if remaining_amount == 0 else f"⏳ متبقي {remaining_amount:,.0f} ج.م"

                success_message = f"""
✅ تم حفظ الفاتورة بنجاح!

🏷️ نوع الفاتورة: {invoice_type}
👤 العميل: {customer_name}

📊 تفاصيل الفاتورة:
• المجموع الفرعي: {subtotal:,.0f} ج.م
• الخصم: {discount:,.0f} ج.م
• الإجمالي النهائي: {total_amount:,.0f} ج.م
• المدفوع: {paid_amount:,.0f} ج.م
• الحالة: {payment_status}
"""

                QMessageBox.information(self, "✅ تم الحفظ بنجاح", success_message)
                self.clear_invoice()

                return invoice_id

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ في الحفظ", f"حدث خطأ أثناء حفظ الفاتورة:\n{str(e)}")
            return None

    def save_draft_invoice(self):
        """حفظ الفاتورة كمسودة بدون تأثير على المخزون"""
        if not self.items:
            QMessageBox.warning(self, "خطأ", "لا يوجد منتجات في الفاتورة")
            return None

        customer_id = self.customer_combo.currentData()
        customer_name = "عميل نقدي"
        if customer_id is not None:
            try:
                with Session(self.engine) as session:
                    customer = session.query(Customer).get(customer_id)
                    if customer:
                        customer_name = customer.name
                    else:
                        QMessageBox.warning(self, "خطأ", "العميل المحدد غير موجود في قاعدة البيانات")
                        return
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"خطأ في التحقق من العميل: {str(e)}")
                return

        # حساب المجاميع
        subtotal = sum(item['price'] * item['quantity'] for item in self.items)

        # حساب الخصم
        discount = 0
        if self.discount_type.currentText() == "نسبة %":
            discount = subtotal * (self.discount_input.value() / 100)
        else:  # مبلغ
            discount = self.discount_input.value()

        total_amount = subtotal - discount
        paid_amount = self.paid_amount.value()

        try:
            with Session(self.engine) as session:
                # إذا كانت هناك مسودة حالية، قم بتحديثها
                if self.current_draft_id:
                    transaction = session.query(Transaction).get(self.current_draft_id)
                    if transaction and transaction.status == 'draft':
                        # حذف العناصر القديمة
                        session.query(TransactionItem).filter(
                            TransactionItem.transaction_id == self.current_draft_id
                        ).delete()

                        # تحديث بيانات الفاتورة
                        transaction.total_amount = total_amount
                        transaction.paid_amount = paid_amount
                        transaction.discount = discount
                        transaction.customer_id = customer_id
                    else:
                        # إنشاء مسودة جديدة إذا لم تعد المسودة السابقة موجودة
                        self.current_draft_id = None
                        transaction = None

                # إنشاء مسودة جديدة إذا لم تكن هناك مسودة حالية
                if not self.current_draft_id:
                    transaction = Transaction(
                        type=TransactionType.SALE,
                        total_amount=total_amount,
                        paid_amount=paid_amount,
                        discount=discount,
                        customer_id=customer_id,
                        status='draft'  # حالة المسودة
                    )
                    session.add(transaction)
                    session.flush()  # للحصول على ID
                    self.current_draft_id = transaction.id

                # إضافة المنتجات للمسودة (بدون تأثير على المخزون)
                for item in self.items:
                    transaction_item = TransactionItem(
                        transaction=transaction,
                        product_id=item['product_id'],
                        quantity=item['quantity'],
                        price=item['price']
                    )
                    session.add(transaction_item)

                session.commit()

                # رسالة نجاح
                success_message = f"""
✅ تم حفظ المسودة بنجاح!

📝 نوع الفاتورة: مسودة معلقة
👤 العميل: {customer_name}

📊 تفاصيل المسودة:
• المجموع الفرعي: {subtotal:,.0f} ج.م
• الخصم: {discount:,.0f} ج.م
• الإجمالي النهائي: {total_amount:,.0f} ج.م
• المدفوع: {paid_amount:,.0f} ج.م

💡 يمكنك العودة لهذه المسودة في أي وقت لتعديلها أو إكمالها
"""

                QMessageBox.information(self, "✅ تم حفظ المسودة", success_message)
                return self.current_draft_id

        except Exception as e:
            QMessageBox.critical(self, "❌ خطأ في حفظ المسودة", f"حدث خطأ أثناء حفظ المسودة:\n{str(e)}")
            return None

    def show_drafts_dialog(self):
        """عرض نافذة إدارة المسودات"""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget,
                                     QTableWidgetItem, QPushButton, QLabel, QHeaderView)

        dialog = QDialog(self)

        # إعداد النافذة مع خاصية التكبير
        from utils.dialog_utils import setup_large_dialog
        setup_large_dialog(dialog, "إدارة الفواتير المعلقة", 1000, 600, 1200, 700)

        dialog.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
                color: #2C3E50;
            }
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                padding: 10px;
            }
            QTableWidget {
                background-color: white;
                border: 2px solid #3498DB;
                border-radius: 8px;
                font-size: 14px;
                gridline-color: #BDC3C7;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ECF0F1;
            }
            QTableWidget::item:selected {
                background-color: #3498DB;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495E;
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
                font-size: 14px;
            }
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                min-width: 120px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)

        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # عنوان
        title = QLabel("📋 الفواتير المعلقة (المسودات)")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # جدول المسودات
        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels([
            "رقم المسودة", "العميل", "التاريخ", "المجموع", "المدفوع", "المتبقي"
        ])

        # تحميل المسودات
        try:
            with Session(self.engine) as session:
                drafts = session.query(Transaction).filter(
                    Transaction.status == 'draft',
                    Transaction.type == TransactionType.SALE
                ).order_by(Transaction.id.desc()).all()

                table.setRowCount(len(drafts))

                for row, draft in enumerate(drafts):
                    # رقم المسودة
                    table.setItem(row, 0, QTableWidgetItem(f"D{draft.id:06d}"))

                    # العميل
                    customer_name = "عميل نقدي"
                    if draft.customer:
                        customer_name = draft.customer.name
                    table.setItem(row, 1, QTableWidgetItem(customer_name))

                    # التاريخ
                    date_str = draft.date.strftime("%Y-%m-%d %H:%M") if draft.date else ""
                    table.setItem(row, 2, QTableWidgetItem(date_str))

                    # المجموع
                    table.setItem(row, 3, QTableWidgetItem(f"{draft.total_amount:,.0f} ج.م"))

                    # المدفوع
                    table.setItem(row, 4, QTableWidgetItem(f"{draft.paid_amount:,.0f} ج.م"))

                    # المتبقي
                    remaining = draft.total_amount - draft.paid_amount
                    table.setItem(row, 5, QTableWidgetItem(f"{remaining:,.0f} ج.م"))

                    # حفظ ID المسودة في البيانات
                    table.item(row, 0).setData(Qt.UserRole, draft.id)

        except Exception as e:
            QMessageBox.critical(dialog, "خطأ", f"حدث خطأ في تحميل المسودات:\n{str(e)}")
            return

        # تحسين عرض الجدول
        header = table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setAlternatingRowColors(True)

        layout.addWidget(table)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        load_btn = QPushButton("📂 تحميل المسودة")
        load_btn.setStyleSheet("QPushButton { background-color: #28A745; }")

        edit_btn = QPushButton("✏️ تعديل المسودة")
        edit_btn.setStyleSheet("QPushButton { background-color: #FFC107; color: #212529; }")

        convert_btn = QPushButton("✅ تحويل لفاتورة نهائية")
        convert_btn.setStyleSheet("QPushButton { background-color: #007BFF; }")

        delete_btn = QPushButton("🗑️ حذف المسودة")
        delete_btn.setStyleSheet("QPushButton { background-color: #DC3545; }")

        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet("QPushButton { background-color: #6C757D; }")

        buttons_layout.addWidget(load_btn)
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(convert_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        # ربط الأحداث
        def load_draft():
            current_row = table.currentRow()
            if current_row >= 0:
                draft_id = table.item(current_row, 0).data(Qt.UserRole)
                self.load_draft_invoice(draft_id)
                dialog.accept()

        def edit_draft():
            current_row = table.currentRow()
            if current_row >= 0:
                draft_id = table.item(current_row, 0).data(Qt.UserRole)
                self.load_draft_invoice(draft_id)
                dialog.accept()

        def convert_draft():
            current_row = table.currentRow()
            if current_row >= 0:
                draft_id = table.item(current_row, 0).data(Qt.UserRole)
                if self.convert_draft_to_final(draft_id):
                    dialog.accept()

        def delete_draft():
            current_row = table.currentRow()
            if current_row >= 0:
                draft_id = table.item(current_row, 0).data(Qt.UserRole)
                reply = QMessageBox.question(
                    dialog,
                    "تأكيد الحذف",
                    "هل أنت متأكد من حذف هذه المسودة؟\nلا يمكن التراجع عن هذا الإجراء.",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    if self.delete_draft_invoice(draft_id):
                        table.removeRow(current_row)

        load_btn.clicked.connect(load_draft)
        edit_btn.clicked.connect(edit_draft)
        convert_btn.clicked.connect(convert_draft)
        delete_btn.clicked.connect(delete_draft)
        close_btn.clicked.connect(dialog.reject)

        # تفعيل الأزرار عند اختيار صف
        def on_selection_changed():
            has_selection = table.currentRow() >= 0
            load_btn.setEnabled(has_selection)
            edit_btn.setEnabled(has_selection)
            convert_btn.setEnabled(has_selection)
            delete_btn.setEnabled(has_selection)

        table.itemSelectionChanged.connect(on_selection_changed)
        on_selection_changed()  # تحديث حالة الأزرار في البداية

        dialog.exec_()

    def load_draft_invoice(self, draft_id):
        """تحميل مسودة فاتورة للتعديل"""
        try:
            with Session(self.engine) as session:
                draft = session.query(Transaction).filter(
                    Transaction.id == draft_id,
                    Transaction.status == 'draft'
                ).first()

                if not draft:
                    QMessageBox.warning(self, "خطأ", "المسودة غير موجودة أو تم تحويلها بالفعل")
                    return False

                # مسح الفاتورة الحالية
                self.clear_invoice()

                # تحديد المسودة الحالية
                self.current_draft_id = draft_id

                # تحميل بيانات العميل
                if draft.customer_id:
                    # البحث عن العميل في القائمة المنسدلة
                    for i in range(self.customer_combo.count()):
                        if self.customer_combo.itemData(i) == draft.customer_id:
                            self.customer_combo.setCurrentIndex(i)
                            break

                # تحميل المنتجات
                items = session.query(TransactionItem).filter(
                    TransactionItem.transaction_id == draft_id
                ).all()

                for item in items:
                    product = item.product
                    if product:
                        self.add_product_to_table(product, item.quantity, item.price)

                # تحميل بيانات الدفع والخصم
                self.paid_amount.setValue(draft.paid_amount)

                # تحميل الخصم
                if draft.discount > 0:
                    subtotal = sum(item.price * item.quantity for item in items)
                    if subtotal > 0:
                        discount_percentage = (draft.discount / subtotal) * 100
                        if discount_percentage <= 100:
                            self.discount_type.setCurrentText("نسبة %")
                            self.discount_input.setValue(discount_percentage)
                        else:
                            self.discount_type.setCurrentText("مبلغ")
                            self.discount_input.setValue(draft.discount)
                    else:
                        self.discount_type.setCurrentText("مبلغ")
                        self.discount_input.setValue(draft.discount)

                # تحديث المجاميع
                self.update_total()

                QMessageBox.information(
                    self,
                    "✅ تم التحميل",
                    f"تم تحميل المسودة رقم D{draft_id:06d} بنجاح!\n\nيمكنك الآن تعديلها أو إكمالها."
                )

                return True

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل المسودة:\n{str(e)}")
            return False

    def convert_draft_to_final(self, draft_id):
        """تحويل المسودة إلى فاتورة نهائية"""
        try:
            with Session(self.engine) as session:
                draft = session.query(Transaction).filter(
                    Transaction.id == draft_id,
                    Transaction.status == 'draft'
                ).first()

                if not draft:
                    QMessageBox.warning(self, "خطأ", "المسودة غير موجودة أو تم تحويلها بالفعل")
                    return False

                # التحقق من توفر المنتجات في المخزون
                items = session.query(TransactionItem).filter(
                    TransactionItem.transaction_id == draft_id
                ).all()

                insufficient_stock = []
                for item in items:
                    product = item.product
                    if product and product.quantity < item.quantity:
                        insufficient_stock.append(f"• {product.name}: متوفر {product.quantity}, مطلوب {item.quantity}")

                if insufficient_stock:
                    QMessageBox.warning(
                        self,
                        "مخزون غير كافي",
                        f"لا يمكن تحويل المسودة لفاتورة نهائية بسبب نقص المخزون:\n\n" +
                        "\n".join(insufficient_stock)
                    )
                    return False

                # تأكيد التحويل
                reply = QMessageBox.question(
                    self,
                    "تأكيد التحويل",
                    f"هل أنت متأكد من تحويل المسودة رقم D{draft_id:06d} إلى فاتورة نهائية؟\n\n"
                    "سيتم:\n"
                    "• خصم المنتجات من المخزون\n"
                    "• تحديث رصيد العميل (إن وجد)\n"
                    "• تحويل حالة الفاتورة إلى نهائية\n\n"
                    "لا يمكن التراجع عن هذا الإجراء.",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return False

                # تحويل المسودة إلى فاتورة نهائية
                draft.status = 'pending'  # تغيير الحالة من draft إلى pending

                # تحديث المخزون
                for item in items:
                    product = item.product
                    if product:
                        product.quantity -= item.quantity

                # تحديث رصيد العميل (إذا كان هناك مبلغ متبقي)
                remaining_amount = draft.total_amount - draft.paid_amount
                if draft.customer_id and remaining_amount > 0:
                    customer = session.query(Customer).get(draft.customer_id)
                    if customer:
                        customer.balance += remaining_amount

                session.commit()

                # رسالة نجاح
                customer_name = "عميل نقدي"
                if draft.customer:
                    customer_name = draft.customer.name

                success_message = f"""
✅ تم تحويل المسودة بنجاح!

🏷️ رقم الفاتورة: #{draft_id:06d}
👤 العميل: {customer_name}
💰 المجموع: {draft.total_amount:,.0f} ج.م
💳 المدفوع: {draft.paid_amount:,.0f} ج.م
⏳ المتبقي: {remaining_amount:,.0f} ج.م

تم تحديث المخزون وأرصدة العملاء بنجاح.
"""

                QMessageBox.information(self, "✅ تم التحويل", success_message)

                # مسح المسودة الحالية إذا كانت هي نفسها
                if self.current_draft_id == draft_id:
                    self.current_draft_id = None
                    self.clear_invoice()

                return True

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحويل المسودة:\n{str(e)}")
            return False

    def delete_draft_invoice(self, draft_id):
        """حذف مسودة فاتورة"""
        try:
            with Session(self.engine) as session:
                # حذف عناصر الفاتورة
                session.query(TransactionItem).filter(
                    TransactionItem.transaction_id == draft_id
                ).delete()

                # حذف الفاتورة
                session.query(Transaction).filter(
                    Transaction.id == draft_id,
                    Transaction.status == 'draft'
                ).delete()

                session.commit()

                # مسح المسودة الحالية إذا كانت هي نفسها
                if self.current_draft_id == draft_id:
                    self.current_draft_id = None
                    self.clear_invoice()

                QMessageBox.information(self, "✅ تم الحذف", f"تم حذف المسودة رقم D{draft_id:06d} بنجاح")
                return True

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حذف المسودة:\n{str(e)}")
            return False

    def show_add_customer_dialog(self):
        """عرض نافذة إضافة عميل جديد"""
        dialog = QDialog(self)
        dialog.setWindowTitle("إضافة عميل جديد")

        # إضافة أزرار التحكم في النافذة (تكبير/تصغير)
        dialog.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)

        dialog.setMinimumSize(500, 400)
        dialog.resize(600, 500)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #F8F9FA;
                border-radius: 10px;
            }
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2C3E50;
                margin-bottom: 5px;
            }
            QLineEdit {
                padding: 10px;
                border: 2px solid #3498DB;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                margin-bottom: 10px;
            }
            QLineEdit:focus {
                border-color: #2980B9;
                background-color: #F8F9FA;
            }
            QPushButton {
                padding: 12px 20px;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 8px;
                min-width: 100px;
            }
            QPushButton#save_btn {
                background-color: #28A745;
                color: white;
            }
            QPushButton#save_btn:hover {
                background-color: #218838;
            }
            QPushButton#cancel_btn {
                background-color: #6C757D;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #5A6268;
            }
        """)

        layout = QVBoxLayout()
        dialog.setLayout(layout)

        # عنوان النافذة
        title_label = QLabel("➕ إضافة عميل جديد")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2980B9; text-align: center; margin-bottom: 20px;")
        layout.addWidget(title_label)

        # حقول البيانات
        form_layout = QFormLayout()

        # اسم العميل (مطلوب)
        name_input = QLineEdit()
        name_input.setPlaceholderText("أدخل اسم العميل...")
        form_layout.addRow("👤 اسم العميل *:", name_input)

        # رقم الهاتف
        phone_input = QLineEdit()
        phone_input.setPlaceholderText("أدخل رقم الهاتف...")
        form_layout.addRow("📱 رقم الهاتف:", phone_input)

        # العنوان
        address_input = QLineEdit()
        address_input.setPlaceholderText("أدخل العنوان...")
        form_layout.addRow("🏠 العنوان:", address_input)

        # البريد الإلكتروني
        email_input = QLineEdit()
        email_input.setPlaceholderText("أدخل البريد الإلكتروني...")
        form_layout.addRow("📧 البريد الإلكتروني:", email_input)

        # الرقم الضريبي
        tax_input = QLineEdit()
        tax_input.setPlaceholderText("أدخل الرقم الضريبي...")
        form_layout.addRow("🧾 الرقم الضريبي:", tax_input)

        layout.addLayout(form_layout)

        # ملاحظة
        note_label = QLabel("* الحقول المطلوبة")
        note_label.setStyleSheet("font-size: 12px; color: #E74C3C; font-style: italic;")
        layout.addWidget(note_label)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.clicked.connect(dialog.reject)

        save_btn = QPushButton("💾 حفظ العميل")
        save_btn.setObjectName("save_btn")

        def save_customer():
            name = name_input.text().strip()
            if not name:
                QMessageBox.warning(dialog, "تنبيه", "يرجى إدخال اسم العميل")
                name_input.setFocus()
                return

            try:
                with Session(self.engine) as session:
                    # التحقق من عدم وجود عميل بنفس الاسم
                    existing = session.query(Customer).filter(Customer.name == name).first()
                    if existing:
                        QMessageBox.warning(dialog, "تنبيه", f"يوجد عميل بالاسم '{name}' مسبقاً")
                        name_input.setFocus()
                        return

                    # إنشاء العميل الجديد
                    new_customer = Customer(
                        name=name,
                        phone=phone_input.text().strip() or None,
                        address=address_input.text().strip() or None,
                        email=email_input.text().strip() or None,
                        tax_number=tax_input.text().strip() or None,
                        balance=0.0,
                        credit_limit=0.0,
                        is_active=True
                    )

                    session.add(new_customer)
                    session.commit()

                    # تحديث قائمة العملاء
                    self.load_customers()

                    # اختيار العميل الجديد تلقائياً
                    for i in range(self.customer_combo.count()):
                        if self.customer_combo.itemData(i) == new_customer.id:
                            self.customer_combo.setCurrentIndex(i)
                            break

                    QMessageBox.information(dialog, "✅ نجح الحفظ", f"تم إضافة العميل '{name}' بنجاح!")
                    dialog.accept()

            except Exception as e:
                QMessageBox.critical(dialog, "❌ خطأ", f"حدث خطأ أثناء حفظ العميل:\n{str(e)}")

        save_btn.clicked.connect(save_customer)

        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

        # تركيز على حقل الاسم
        name_input.setFocus()

        # عرض النافذة
        dialog.exec_()

    def save_and_print(self):
        """حفظ الفاتورة وطباعتها"""
        # حفظ الفاتورة أولاً
        invoice_id = self.save_invoice()

        # إذا تم الحفظ بنجاح، فتح نافذة الطباعة بتقسيم الأرباع
        if invoice_id:
            try:
                show_advanced_print_dialog(self.engine, invoice_id, self)
            except Exception as e:
                QMessageBox.critical(self, "خطأ في الطباعة", f"تم حفظ الفاتورة بنجاح ولكن حدث خطأ أثناء الطباعة:\n{str(e)}")

    def print_invoice(self, invoice_id=None):
        """طباعة الفاتورة"""
        if invoice_id is None:
            QMessageBox.warning(self, "خطأ", "لا يوجد فاتورة محددة للطباعة")
            return

        try:
            show_advanced_print_dialog(self.engine, invoice_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء طباعة الفاتورة:\n{str(e)}")

    def clear_invoice(self):
        self.products_table.setRowCount(0)
        self.items.clear()
        self.paid_amount.setValue(0)

        # مسح حقل الخصم الجديد
        if hasattr(self, 'discount_input'):
            self.discount_input.setValue(0)

        # مسح معرف المسودة الحالية
        self.current_draft_id = None

        self.update_total()

        # إعادة تعيين رقم الفاتورة
        self.generate_invoice_number()

    def ask_update_product_price(self, row, new_price, old_price):
        """سؤال المستخدم عن تحديث سعر المنتج في المخزون"""
        try:
            # الحصول على معلومات المنتج
            product_id = self.items[row]['product_id']

            with Session(self.engine) as session:
                product = session.query(Product).get(product_id)
                if not product:
                    return

                # إنشاء نافذة التأكيد المخصصة
                dialog = QDialog(self)
                dialog.setWindowTitle("تحديث سعر المنتج")
                dialog.setFixedSize(500, 350)
                dialog.setModal(True)

                # تصميم النافذة
                layout = QVBoxLayout(dialog)
                layout.setSpacing(20)

                # أيقونة وعنوان
                header_layout = QHBoxLayout()

                icon_label = QLabel("💰")
                icon_label.setStyleSheet("font-size: 48px;")
                icon_label.setAlignment(Qt.AlignCenter)

                title_label = QLabel("تحديث سعر المنتج")
                title_label.setStyleSheet("""
                    font-size: 20px;
                    font-weight: bold;
                    color: #2C3E50;
                    margin-left: 15px;
                """)
                title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

                header_layout.addWidget(icon_label)
                header_layout.addWidget(title_label)
                header_layout.addStretch()
                layout.addLayout(header_layout)

                # معلومات المنتج
                info_frame = QFrame()
                info_frame.setStyleSheet("""
                    QFrame {
                        background-color: #F8F9FA;
                        border: 2px solid #E9ECEF;
                        border-radius: 10px;
                        padding: 15px;
                    }
                """)
                info_layout = QVBoxLayout(info_frame)

                product_name_label = QLabel(f"📦 المنتج: {product.name}")
                product_name_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #495057;")

                old_price_label = QLabel(f"💵 السعر الحالي في المخزون: {product.sale_price:,.0f} ج.م")
                old_price_label.setStyleSheet("font-size: 14px; color: #6C757D;")

                new_price_label = QLabel(f"🆕 السعر الجديد في الفاتورة: {new_price:,.0f} ج.م")
                new_price_label.setStyleSheet("font-size: 14px; color: #28A745; font-weight: bold;")

                info_layout.addWidget(product_name_label)
                info_layout.addWidget(old_price_label)
                info_layout.addWidget(new_price_label)
                layout.addWidget(info_frame)

                # السؤال الرئيسي
                question_label = QLabel("هل تريد تحديث سعر هذا المنتج في المخزون ليصبح السعر الجديد هو السعر الأساسي؟")
                question_label.setStyleSheet("""
                    font-size: 16px;
                    color: #495057;
                    font-weight: bold;
                    text-align: center;
                """)
                question_label.setWordWrap(True)
                question_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(question_label)

                # الأزرار
                buttons_layout = QHBoxLayout()

                yes_btn = QPushButton("✅ نعم، حدث السعر في المخزون")
                yes_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #28A745;
                        color: white;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 12px 20px;
                        border: none;
                        border-radius: 8px;
                        min-width: 180px;
                    }
                    QPushButton:hover {
                        background-color: #218838;
                    }
                    QPushButton:pressed {
                        background-color: #1E7E34;
                    }
                """)

                no_btn = QPushButton("❌ لا، فقط في هذه الفاتورة")
                no_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #DC3545;
                        color: white;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 12px 20px;
                        border: none;
                        border-radius: 8px;
                        min-width: 180px;
                    }
                    QPushButton:hover {
                        background-color: #C82333;
                    }
                    QPushButton:pressed {
                        background-color: #BD2130;
                    }
                """)

                buttons_layout.addWidget(yes_btn)
                buttons_layout.addWidget(no_btn)
                layout.addLayout(buttons_layout)

                # ربط الأزرار
                def update_price():
                    try:
                        # التحقق من أن سعر البيع أكبر من سعر الشراء
                        if new_price <= product.purchase_price:
                            QMessageBox.warning(
                                self,
                                "⚠️ تحذير: سعر غير مربح",
                                f"لا يمكن تحديث السعر!\n\n"
                                f"📦 المنتج: {product.name}\n"
                                f"💰 سعر الشراء: {product.purchase_price:,.0f} ج.م\n"
                                f"🔴 السعر المطلوب: {new_price:,.0f} ج.م\n\n"
                                f"⚠️ سعر البيع يجب أن يكون أكبر من سعر الشراء لضمان الربحية!\n"
                                f"💡 الحد الأدنى المسموح: {product.purchase_price + 1:,.0f} ج.م"
                            )
                            return

                        # تحديث سعر المنتج في قاعدة البيانات
                        product.sale_price = new_price
                        session.commit()

                        QMessageBox.information(
                            self,
                            "✅ تم التحديث",
                            f"تم تحديث سعر '{product.name}' في المخزون إلى {new_price:,.0f} ج.م"
                        )
                    except Exception as e:
                        QMessageBox.critical(
                            self,
                            "❌ خطأ",
                            f"حدث خطأ أثناء تحديث السعر:\n{str(e)}"
                        )
                    dialog.accept()

                def keep_old_price():
                    dialog.accept()

                yes_btn.clicked.connect(update_price)
                no_btn.clicked.connect(keep_old_price)

                # عرض النافذة
                dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "❌ خطأ",
                f"حدث خطأ أثناء معالجة تحديث السعر:\n{str(e)}"
            )

    def check_low_stock_alert(self, product, old_quantity):
        """فحص فوري للمخزون المنخفض وإظهار تنبيه منبثق"""
        try:
            # التحقق من وجود حد أدنى محدد للمنتج
            if not product.min_quantity or product.min_quantity <= 0:
                return

            # التحقق من أن المخزون وصل للحد الأدنى أو أقل
            if product.quantity <= product.min_quantity:
                # التحقق من أن هذا انخفاض جديد (لم يكن منخفضاً من قبل)
                if old_quantity > product.min_quantity:
                    # إظهار تنبيه منبثق فوري
                    self.show_low_stock_popup(product)

        except Exception as e:
            print(f"خطأ في فحص المخزون المنخفض: {e}")

    def show_low_stock_popup(self, product):
        """إظهار نافذة تنبيه منبثقة للمخزون المنخفض"""
        try:
            # تحديد نوع التحذير حسب مستوى المخزون
            if product.quantity == 0:
                alert_type = "🚨 تحذير: نفد المخزون!"
                alert_color = "#DC3545"  # أحمر
                alert_message = f"نفد مخزون المنتج '{product.name}' تماماً!"
            elif product.quantity < product.min_quantity:
                alert_type = "⚠️ تحذير: مخزون منخفض جداً!"
                alert_color = "#FD7E14"  # برتقالي
                alert_message = f"مخزون المنتج '{product.name}' أقل من الحد الأدنى!"
            else:
                alert_type = "🔔 تنبيه: وصل للحد الأدنى"
                alert_color = "#FFC107"  # أصفر
                alert_message = f"مخزون المنتج '{product.name}' وصل للحد الأدنى!"

            # إنشاء نافذة التنبيه المنبثقة
            msg = QMessageBox(self)
            msg.setWindowTitle(alert_type)
            msg.setIcon(QMessageBox.Warning)

            # النص الرئيسي
            main_text = f"""
{alert_message}

📦 المنتج: {product.name}
📊 الكمية الحالية: {product.quantity:,}
⚠️ الحد الأدنى المطلوب: {product.min_quantity:,}
🔴 النقص: {product.min_quantity - product.quantity:,}

💡 يُنصح بإعادة التوريد فوراً لتجنب نفاد المخزون!
            """

            msg.setText(main_text)

            # تخصيص الألوان والتنسيق
            msg.setStyleSheet(f"""
                QMessageBox {{
                    background-color: white;
                    border: 3px solid {alert_color};
                    border-radius: 10px;
                }}
                QMessageBox QLabel {{
                    color: #2C3E50;
                    font-size: 14px;
                    padding: 15px;
                }}
                QMessageBox QPushButton {{
                    background-color: {alert_color};
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    min-width: 100px;
                }}
                QMessageBox QPushButton:hover {{
                    background-color: #218838;
                }}
            """)

            # إضافة أزرار مخصصة
            msg.addButton("✅ فهمت", QMessageBox.AcceptRole)
            msg.addButton("📋 عرض التنبيهات", QMessageBox.ActionRole)

            # عرض النافذة والحصول على الاستجابة
            result = msg.exec_()

            # إذا اختار المستخدم "عرض التنبيهات"
            if result == 1:  # ActionRole button
                self.show_notifications_tab()

        except Exception as e:
            print(f"خطأ في إظهار تنبيه المخزون: {e}")

    def show_notifications_tab(self):
        """الانتقال لتبويب التنبيهات في النافذة الرئيسية"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'tab_widget'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'tab_widget'):
                # البحث عن تبويب التنبيهات
                for i in range(main_window.tab_widget.count()):
                    if "تنبيهات" in main_window.tab_widget.tabText(i):
                        main_window.tab_widget.setCurrentIndex(i)
                        break

        except Exception as e:
            print(f"خطأ في الانتقال لتبويب التنبيهات: {e}")