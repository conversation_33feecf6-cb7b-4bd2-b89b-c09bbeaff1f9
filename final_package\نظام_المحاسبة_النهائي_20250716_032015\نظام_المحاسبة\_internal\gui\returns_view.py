"""
صفحة عرض فواتير المرتجعات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QComboBox, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox, QDialog,
                             QFrame, QGridLayout, QHeaderView, QDateEdit, QTextEdit,
                             QGroupBox, QFormLayout, QCheckBox, QSplitter, QTabWidget)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QIcon, QColor, QPixmap, QFont
from sqlalchemy.orm import Session
from database.models import Transaction, TransactionItem, Product, Customer, Supplier, TransactionType
from utils.dialog_utils import setup_large_dialog
from datetime import datetime, timedelta


class ReturnsViewWidget(QWidget):
    """واجهة عرض فواتير مرتجعات المبيعات"""
    
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        self.load_returns()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # العنوان الرئيسي
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid white;
                border-radius: 10px;
                margin: 10px;
                padding: 20px;
            }
        """)
        title_layout = QHBoxLayout()

        title_label = QLabel("📋 عرض فواتير مرتجعات المبيعات")
        title_label.setStyleSheet("""
            QLabel {
                color: #8E44AD;
                font-size: 28px;
                font-weight: bold;
                background: transparent;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E44AD;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: 2px solid #8E44AD;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #7D3C98;
                border-color: #7D3C98;
            }
        """)
        refresh_btn.clicked.connect(self.load_returns)
        title_layout.addWidget(refresh_btn)
        
        title_frame.setLayout(title_layout)
        layout.addWidget(title_frame)
        
        # إطار البحث والفلترة
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        search_layout = QHBoxLayout()
        
        # حقل البحث
        search_layout.addWidget(QLabel("🔍 البحث:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث برقم المرتجع أو رقم الفاتورة الأصلية...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                min-width: 250px;
            }
            QLineEdit:focus {
                border-color: #8E44AD;
            }
        """)
        self.search_input.textChanged.connect(self.filter_returns)
        search_layout.addWidget(self.search_input)
        
        # فلتر نوع المرتجع
        search_layout.addWidget(QLabel("📊 النوع:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["جميع المرتجعات", "مرتجع مبيعات", "مرتجع مشتريات"])
        self.type_filter.setStyleSheet("""
            QComboBox {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                min-width: 150px;
            }
        """)
        self.type_filter.currentTextChanged.connect(self.filter_returns)
        search_layout.addWidget(self.type_filter)
        
        # فلتر التاريخ
        search_layout.addWidget(QLabel("📅 من:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        self.date_from.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
            }
        """)
        self.date_from.dateChanged.connect(self.filter_returns)
        search_layout.addWidget(self.date_from)
        
        search_layout.addWidget(QLabel("إلى:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        self.date_to.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
            }
        """)
        self.date_to.dateChanged.connect(self.filter_returns)
        search_layout.addWidget(self.date_to)
        
        search_layout.addStretch()
        
        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)
        
        # جدول المرتجعات
        self.returns_table = QTableWidget()
        self.returns_table.setStyleSheet("""
            QTableWidget {
                font-size: 16px;
                font-weight: bold;
                gridline-color: #D3D3D3;
                background-color: white;
                alternate-background-color: #F8F9FA;
                selection-background-color: #0078D4;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #E0E0E0;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
                color: white;
            }
            QHeaderView::section {
                background-color: #2C3E50;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
                border: none;
            }
        """)

        self.returns_table.setColumnCount(9)
        self.returns_table.setHorizontalHeaderLabels([
            "رقم المرتجع", "نوع المرتجع", "رقم الفاتورة الأصلية", "التاريخ",
            "العميل/المورد", "إجمالي المرتجع", "المبلغ المسترد", "الملاحظات", "إجراءات"
        ])

        # تحديد عرض الأعمدة - تحسين التخطيط
        header = self.returns_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Fixed)
        header.resizeSection(0, 130)  # رقم المرتجع
        header.resizeSection(1, 140)  # نوع المرتجع
        header.resizeSection(2, 160)  # رقم الفاتورة الأصلية
        header.resizeSection(3, 130)  # التاريخ
        header.resizeSection(4, 160)  # العميل/المورد
        header.resizeSection(5, 140)  # إجمالي المرتجع
        header.resizeSection(6, 140)  # المبلغ المسترد
        header.resizeSection(7, 220)  # الملاحظات
        header.resizeSection(8, 120)  # إجراءات

        # تعديل ارتفاع الصفوف ليناسب الخط المحسن (نفس صفحة المبيعات)
        self.returns_table.verticalHeader().setDefaultSectionSize(90)

        # منع تحرير الخلايا
        self.returns_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.returns_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.returns_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.returns_table)
        
        # إطار الإحصائيات
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        stats_layout = QHBoxLayout()
        
        # إحصائيات مرتجع المبيعات
        sales_stats_frame = QFrame()
        sales_stats_frame.setStyleSheet("""
            QFrame {
                background-color: #FADBD8;
                border: 2px solid #E74C3C;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        sales_stats_layout = QVBoxLayout()
        
        sales_title = QLabel("📊 مرتجع المبيعات")
        sales_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #E74C3C;")
        sales_stats_layout.addWidget(sales_title)
        
        self.sales_count_label = QLabel("العدد: 0")
        self.sales_total_label = QLabel("الإجمالي: 0.00 جنيه")
        
        for label in [self.sales_count_label, self.sales_total_label]:
            label.setStyleSheet("font-size: 14px; color: #E74C3C;")
            sales_stats_layout.addWidget(label)
        
        sales_stats_frame.setLayout(sales_stats_layout)
        stats_layout.addWidget(sales_stats_frame)
        
        # إحصائيات مرتجع المشتريات
        purchase_stats_frame = QFrame()
        purchase_stats_frame.setStyleSheet("""
            QFrame {
                background-color: #E8DAEF;
                border: 2px solid #9B59B6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        purchase_stats_layout = QVBoxLayout()
        
        purchase_title = QLabel("📊 مرتجع المشتريات")
        purchase_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #9B59B6;")
        purchase_stats_layout.addWidget(purchase_title)
        
        self.purchase_count_label = QLabel("العدد: 0")
        self.purchase_total_label = QLabel("الإجمالي: 0.00 جنيه")
        
        for label in [self.purchase_count_label, self.purchase_total_label]:
            label.setStyleSheet("font-size: 14px; color: #9B59B6;")
            purchase_stats_layout.addWidget(label)
        
        purchase_stats_frame.setLayout(purchase_stats_layout)
        stats_layout.addWidget(purchase_stats_frame)
        
        # إحصائيات إجمالية
        total_stats_frame = QFrame()
        total_stats_frame.setStyleSheet("""
            QFrame {
                background-color: #D5DBDB;
                border: 2px solid #5D6D7E;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        total_stats_layout = QVBoxLayout()
        
        total_title = QLabel("📊 الإجمالي العام")
        total_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #5D6D7E;")
        total_stats_layout.addWidget(total_title)
        
        self.total_count_label = QLabel("العدد: 0")
        self.total_amount_label = QLabel("المبلغ: 0.00 جنيه")
        
        for label in [self.total_count_label, self.total_amount_label]:
            label.setStyleSheet("font-size: 14px; color: #5D6D7E;")
            total_stats_layout.addWidget(label)
        
        total_stats_frame.setLayout(total_stats_layout)
        stats_layout.addWidget(total_stats_frame)
        
        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)
        
        self.setLayout(layout)
    
    def load_returns(self):
        """تحميل فواتير المرتجعات"""
        try:
            with Session(self.engine) as session:
                # جلب جميع المرتجعات
                returns = session.query(Transaction).filter(
                    Transaction.type.in_([TransactionType.SALE_RETURN, TransactionType.PURCHASE_RETURN])
                ).order_by(Transaction.id.desc()).all()
                
                self.all_returns = returns  # حفظ جميع المرتجعات للفلترة
                self.display_returns(returns)
                self.update_statistics(returns)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل المرتجعات:\n{str(e)}")
    
    def display_returns(self, returns):
        """عرض المرتجعات في الجدول"""
        self.returns_table.setRowCount(len(returns))
        
        for row, return_transaction in enumerate(returns):
            # رقم المرتجع
            return_id_item = QTableWidgetItem(f"R{return_transaction.id:06d}")
            return_id_item.setTextAlignment(Qt.AlignCenter)
            if return_transaction.type == TransactionType.SALE_RETURN:
                return_id_item.setBackground(QColor(231, 76, 60, 50))  # أحمر فاتح
            else:
                return_id_item.setBackground(QColor(155, 89, 182, 50))  # بنفسجي فاتح
            self.returns_table.setItem(row, 0, return_id_item)
            
            # نوع المرتجع
            return_type = "مرتجع مبيعات" if return_transaction.type == TransactionType.SALE_RETURN else "مرتجع مشتريات"
            type_item = QTableWidgetItem(return_type)
            type_item.setTextAlignment(Qt.AlignCenter)
            self.returns_table.setItem(row, 1, type_item)
            
            # رقم الفاتورة الأصلية
            original_id = f"#{return_transaction.original_transaction_id:06d}" if return_transaction.original_transaction_id else "غير محدد"
            original_item = QTableWidgetItem(original_id)
            original_item.setTextAlignment(Qt.AlignCenter)
            original_item.setBackground(QColor(52, 152, 219, 50))  # أزرق فاتح
            self.returns_table.setItem(row, 2, original_item)
            
            # التاريخ
            date_str = return_transaction.date.strftime("%Y-%m-%d") if return_transaction.date else ""
            date_item = QTableWidgetItem(date_str)
            date_item.setTextAlignment(Qt.AlignCenter)
            self.returns_table.setItem(row, 3, date_item)
            
            # العميل/المورد
            with Session(self.engine) as session:
                if return_transaction.type == TransactionType.SALE_RETURN and return_transaction.customer_id:
                    customer = session.query(Customer).get(return_transaction.customer_id)
                    contact_name = customer.name if customer else "عميل نقدي"
                elif return_transaction.type == TransactionType.PURCHASE_RETURN and return_transaction.supplier_id:
                    supplier = session.query(Supplier).get(return_transaction.supplier_id)
                    contact_name = supplier.name if supplier else "غير محدد"
                else:
                    contact_name = "غير محدد"
            
            contact_item = QTableWidgetItem(contact_name)
            contact_item.setTextAlignment(Qt.AlignCenter)
            self.returns_table.setItem(row, 4, contact_item)
            
            # المبالغ
            total = return_transaction.total_amount or 0
            paid = return_transaction.paid_amount or 0
            
            total_item = QTableWidgetItem(f"{total:,.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.returns_table.setItem(row, 5, total_item)
            
            paid_item = QTableWidgetItem(f"{paid:,.2f}")
            paid_item.setTextAlignment(Qt.AlignCenter)
            self.returns_table.setItem(row, 6, paid_item)
            
            # الملاحظات
            notes = return_transaction.notes or ""
            notes_item = QTableWidgetItem(notes[:50] + "..." if len(notes) > 50 else notes)
            self.returns_table.setItem(row, 7, notes_item)
            
            # أزرار الإجراءات - تحسين التخطيط
            actions_widget = QWidget()
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(8, 5, 8, 5)
            actions_layout.setSpacing(5)

            # زر عرض التفاصيل - أكبر وأوضح
            view_btn = QPushButton("👁️")
            view_btn.setToolTip("عرض التفاصيل")
            view_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498DB;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px;
                    min-width: 40px;
                    min-height: 35px;
                    font-size: 16px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #2980B9;
                }
            """)
            view_btn.clicked.connect(lambda checked, r=return_transaction.id: self.view_return_details(r))
            actions_layout.addWidget(view_btn)

            # زر طباعة - أكبر وأوضح
            print_btn = QPushButton("🖨️")
            print_btn.setToolTip("طباعة المرتجع")
            print_btn.setStyleSheet("""
                QPushButton {
                    background-color: #27AE60;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px;
                    min-width: 40px;
                    min-height: 35px;
                    font-size: 16px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)
            print_btn.clicked.connect(lambda checked, r=return_transaction.id: self.print_return(r))
            actions_layout.addWidget(print_btn)

            actions_layout.addStretch()
            actions_widget.setLayout(actions_layout)
            self.returns_table.setCellWidget(row, 8, actions_widget)
            
            # حفظ ID المرتجع في البيانات
            self.returns_table.item(row, 0).setData(Qt.UserRole, return_transaction.id)

    def filter_returns(self):
        """فلترة المرتجعات حسب البحث والنوع والتاريخ"""
        search_text = self.search_input.text().lower()
        type_filter = self.type_filter.currentText()
        date_from = self.date_from.date().toPyDate()
        date_to = self.date_to.date().toPyDate()

        filtered_returns = []

        for return_transaction in self.all_returns:
            # فلتر البحث
            if search_text:
                return_id_str = f"r{return_transaction.id:06d}"
                original_id_str = f"{return_transaction.original_transaction_id:06d}" if return_transaction.original_transaction_id else ""

                if (search_text not in return_id_str.lower() and
                    search_text not in original_id_str.lower()):
                    continue

            # فلتر النوع
            if type_filter != "جميع المرتجعات":
                if type_filter == "مرتجع مبيعات" and return_transaction.type != TransactionType.SALE_RETURN:
                    continue
                elif type_filter == "مرتجع مشتريات" and return_transaction.type != TransactionType.PURCHASE_RETURN:
                    continue

            # فلتر التاريخ
            if return_transaction.date:
                return_date = return_transaction.date.date()
                if return_date < date_from or return_date > date_to:
                    continue

            filtered_returns.append(return_transaction)

        self.display_returns(filtered_returns)
        self.update_statistics(filtered_returns)

    def update_statistics(self, returns):
        """تحديث الإحصائيات"""
        sales_returns = [r for r in returns if r.type == TransactionType.SALE_RETURN]
        purchase_returns = [r for r in returns if r.type == TransactionType.PURCHASE_RETURN]

        # إحصائيات مرتجع المبيعات
        sales_count = len(sales_returns)
        sales_total = sum(r.total_amount or 0 for r in sales_returns)

        self.sales_count_label.setText(f"العدد: {sales_count}")
        self.sales_total_label.setText(f"الإجمالي: {sales_total:,.0f} جنيه")

        # إحصائيات مرتجع المشتريات
        purchase_count = len(purchase_returns)
        purchase_total = sum(r.total_amount or 0 for r in purchase_returns)

        self.purchase_count_label.setText(f"العدد: {purchase_count}")
        self.purchase_total_label.setText(f"الإجمالي: {purchase_total:,.0f} جنيه")

        # الإحصائيات الإجمالية
        total_count = len(returns)
        total_amount = sales_total + purchase_total

        self.total_count_label.setText(f"العدد: {total_count}")
        self.total_amount_label.setText(f"المبلغ: {total_amount:,.0f} جنيه")

    def view_return_details(self, return_id):
        """عرض تفاصيل المرتجع"""
        try:
            with Session(self.engine) as session:
                return_transaction = session.query(Transaction).get(return_id)
                if not return_transaction:
                    QMessageBox.warning(self, "خطأ", "المرتجع غير موجود")
                    return

                # إنشاء نافذة التفاصيل
                dialog = ReturnDetailsDialog(self.engine, return_transaction)
                dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التفاصيل:\n{str(e)}")

    def print_return(self, return_id):
        """طباعة المرتجع"""
        try:
            from utils.advanced_invoice_printer import show_advanced_print_dialog
            show_advanced_print_dialog(self.engine, return_id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء الطباعة:\n{str(e)}")


class ReturnDetailsDialog(QDialog):
    """نافذة تفاصيل المرتجع"""

    def __init__(self, engine, return_transaction):
        super().__init__()
        self.engine = engine
        self.return_transaction = return_transaction
        # إعداد النافذة مع خاصية التكبير
        setup_large_dialog(self, f"تفاصيل المرتجع رقم R{return_transaction.id:06d}", 1600, 800, 1600, 900)
        self.setup_ui()
        self.load_details()

    def setup_ui(self):
        layout = QVBoxLayout()

        # معلومات المرتجع
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        info_layout = QGridLayout()

        # تحديد لون العنوان حسب النوع
        if self.return_transaction.type == TransactionType.SALE_RETURN:
            title_color = "#E74C3C"
            title_text = "🔄 تفاصيل مرتجع المبيعات"
        else:
            title_color = "#9B59B6"
            title_text = "🔄 تفاصيل مرتجع المشتريات"

        title_label = QLabel(title_text)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: {title_color};
                padding: 10px;
                background-color: #ECF0F1;
                border-radius: 5px;
                margin-bottom: 15px;
            }}
        """)
        info_layout.addWidget(title_label, 0, 0, 1, 4)

        # معلومات أساسية
        info_layout.addWidget(QLabel("🏷️ رقم المرتجع:"), 1, 0)
        self.return_id_label = QLabel(f"R{self.return_transaction.id:06d}")
        self.return_id_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        info_layout.addWidget(self.return_id_label, 1, 1)

        info_layout.addWidget(QLabel("📋 الفاتورة الأصلية:"), 1, 2)
        original_id = f"#{self.return_transaction.original_transaction_id:06d}" if self.return_transaction.original_transaction_id else "غير محدد"
        self.original_id_label = QLabel(original_id)
        self.original_id_label.setStyleSheet("font-weight: bold; color: #3498DB;")
        info_layout.addWidget(self.original_id_label, 1, 3)

        info_layout.addWidget(QLabel("📅 التاريخ:"), 2, 0)
        date_str = self.return_transaction.date.strftime("%Y-%m-%d %H:%M") if self.return_transaction.date else "غير محدد"
        self.date_label = QLabel(date_str)
        info_layout.addWidget(self.date_label, 2, 1)

        info_layout.addWidget(QLabel("👤 العميل/المورد:"), 2, 2)
        self.contact_label = QLabel("جاري التحميل...")
        info_layout.addWidget(self.contact_label, 2, 3)

        info_layout.addWidget(QLabel("💰 إجمالي المرتجع:"), 3, 0)
        total = self.return_transaction.total_amount or 0
        self.total_label = QLabel(f"{total:,.2f} جنيه مصري")
        self.total_label.setStyleSheet("font-weight: bold; color: #E74C3C;")
        info_layout.addWidget(self.total_label, 3, 1)

        info_layout.addWidget(QLabel("💳 المبلغ المسترد:"), 3, 2)
        paid = self.return_transaction.paid_amount or 0
        self.paid_label = QLabel(f"{paid:,.2f} جنيه مصري")
        self.paid_label.setStyleSheet("font-weight: bold; color: #27AE60;")
        info_layout.addWidget(self.paid_label, 3, 3)

        info_layout.addWidget(QLabel("📝 الملاحظات:"), 4, 0)
        notes = self.return_transaction.notes or "لا توجد ملاحظات"
        self.notes_label = QLabel(notes)
        self.notes_label.setWordWrap(True)
        info_layout.addWidget(self.notes_label, 4, 1, 1, 3)

        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)

        # جدول منتجات المرتجع
        items_label = QLabel("📦 منتجات المرتجع:")
        items_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2C3E50; margin: 10px;")
        layout.addWidget(items_label)

        self.items_table = QTableWidget()
        self.items_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                gridline-color: #BDC3C7;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #34495E;
                color: white;
                padding: 8px;
                border: 1px solid #2C3E50;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        self.items_table.setColumnCount(4)
        self.items_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية", "السعر", "الإجمالي"
        ])

        # تحديد عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.resizeSection(0, 300)  # المنتج
        header.resizeSection(1, 100)  # الكمية
        header.resizeSection(2, 120)  # السعر
        header.resizeSection(3, 120)  # الإجمالي

        self.items_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)

        layout.addWidget(self.items_table)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        print_btn = QPushButton("🖨️ طباعة المرتجع")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        print_btn.clicked.connect(self.print_return)
        buttons_layout.addWidget(print_btn)

        buttons_layout.addStretch()

        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def load_details(self):
        """تحميل تفاصيل المرتجع"""
        try:
            with Session(self.engine) as session:
                # تحميل اسم العميل/المورد
                if self.return_transaction.type == TransactionType.SALE_RETURN and self.return_transaction.customer_id:
                    customer = session.query(Customer).get(self.return_transaction.customer_id)
                    contact_name = customer.name if customer else "عميل نقدي"
                elif self.return_transaction.type == TransactionType.PURCHASE_RETURN and self.return_transaction.supplier_id:
                    supplier = session.query(Supplier).get(self.return_transaction.supplier_id)
                    contact_name = supplier.name if supplier else "غير محدد"
                else:
                    contact_name = "غير محدد"

                self.contact_label.setText(contact_name)

                # تحميل منتجات المرتجع
                items = session.query(TransactionItem, Product).join(Product).filter(
                    TransactionItem.transaction_id == self.return_transaction.id
                ).all()

                self.items_table.setRowCount(len(items))

                for row, (item, product) in enumerate(items):
                    # اسم المنتج
                    product_item = QTableWidgetItem(product.name)
                    product_item.setTextAlignment(Qt.AlignCenter)
                    self.items_table.setItem(row, 0, product_item)

                    # الكمية
                    qty_item = QTableWidgetItem(str(item.quantity))
                    qty_item.setTextAlignment(Qt.AlignCenter)
                    self.items_table.setItem(row, 1, qty_item)

                    # السعر
                    price_item = QTableWidgetItem(f"{item.price:,.2f}")
                    price_item.setTextAlignment(Qt.AlignCenter)
                    self.items_table.setItem(row, 2, price_item)

                    # الإجمالي
                    total = item.quantity * item.price
                    total_item = QTableWidgetItem(f"{total:,.2f}")
                    total_item.setTextAlignment(Qt.AlignCenter)
                    self.items_table.setItem(row, 3, total_item)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل التفاصيل:\n{str(e)}")

    def print_return(self):
        """طباعة المرتجع"""
        try:
            from utils.advanced_invoice_printer import show_advanced_print_dialog
            show_advanced_print_dialog(self.engine, self.return_transaction.id, self)
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
