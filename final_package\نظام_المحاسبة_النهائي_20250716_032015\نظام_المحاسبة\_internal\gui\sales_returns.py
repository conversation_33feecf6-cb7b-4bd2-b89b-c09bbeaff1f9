"""
صفحة مرتجع المبيعات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QComboBox, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox, QDialog,
                             QFrame, QGridLayout, QHeaderView, QDateEdit, QTextEdit,
                             QGroupBox, QFormLayout, QCheckBox, QSizePolicy)
from PyQt5.QtWidgets import QSplitter
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QColor
from sqlalchemy.orm import Session
from database.models import Transaction, TransactionItem, Product, Customer, TransactionType
from utils.dialog_utils import setup_large_dialog
from datetime import datetime


class InvoiceSearchDialog(QDialog):
    """نافذة البحث عن الفواتير للمرتجع"""
    
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.selected_invoice = None
        # إعداد النافذة مع خاصية التكبير
        setup_large_dialog(self, "🔍 البحث عن فاتورة للمرتجع", 900, 600, 1000, 700)
        self.setup_ui()
        self.load_invoices()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # إطار البحث
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        search_layout = QHBoxLayout()
        
        # حقل البحث
        search_layout.addWidget(QLabel("🔍 البحث:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث برقم الفاتورة أو اسم العميل...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)
        self.search_input.textChanged.connect(self.filter_invoices)
        search_layout.addWidget(self.search_input)
        
        # فلتر الحالة
        search_layout.addWidget(QLabel("📊 الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الفواتير", "مدفوعة", "جزئية", "غير مدفوعة"])
        self.status_filter.setStyleSheet("""
            QComboBox {
                padding: 8px;
                font-size: 14px;
                border: 2px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                min-width: 120px;
            }
        """)
        self.status_filter.currentTextChanged.connect(self.filter_invoices)
        search_layout.addWidget(self.status_filter)
        
        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)
        
        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                background-color: white;
                gridline-color: #DEE2E6;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #DEE2E6;
            }
            QHeaderView::section {
                background-color: #E74C3C;
                color: white;
                padding: 15px;
                border: 1px solid #C0392B;
                font-weight: bold;
                font-size: 16px;
            }
            QTableWidget::item:selected {
                background-color: #FADBD8;
                color: #C0392B;
            }
        """)
        
        self.invoices_table.setColumnCount(7)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "العميل", "الإجمالي", "المدفوع", "المتبقي", "الحالة"
        ])
        
        # تحديد عرض الأعمدة
        header = self.invoices_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 120)  # رقم الفاتورة
        header.resizeSection(1, 120)  # التاريخ
        header.resizeSection(2, 150)  # العميل
        header.resizeSection(3, 120)  # الإجمالي
        header.resizeSection(4, 120)  # المدفوع
        header.resizeSection(5, 120)  # المتبقي
        
        # منع تحرير الخلايا
        self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.doubleClicked.connect(self.select_invoice)
        
        layout.addWidget(self.invoices_table)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        select_btn = QPushButton("✅ اختيار الفاتورة")
        select_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        select_btn.clicked.connect(self.select_invoice)
        buttons_layout.addWidget(select_btn)
        
        buttons_layout.addStretch()
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def load_invoices(self):
        """تحميل فواتير المبيعات"""
        try:
            with Session(self.engine) as session:
                # جلب فواتير المبيعات فقط
                invoices = session.query(Transaction).filter(
                    Transaction.type == TransactionType.SALE
                ).order_by(Transaction.id.desc()).all()
                
                self.all_invoices = invoices  # حفظ جميع الفواتير للفلترة
                self.display_invoices(invoices)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل الفواتير:\n{str(e)}")
    
    def display_invoices(self, invoices):
        """عرض الفواتير في الجدول"""
        self.invoices_table.setRowCount(len(invoices))
        
        for row, invoice in enumerate(invoices):
            # رقم الفاتورة
            self.invoices_table.setItem(row, 0, QTableWidgetItem(f"{invoice.id:06d}"))
            
            # التاريخ
            date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
            self.invoices_table.setItem(row, 1, QTableWidgetItem(date_str))
            
            # العميل
            with Session(self.engine) as session:
                customer = session.query(Customer).get(invoice.customer_id) if invoice.customer_id else None
                customer_name = customer.name if customer else "عميل نقدي"
            self.invoices_table.setItem(row, 2, QTableWidgetItem(customer_name))
            
            # المبالغ
            total = invoice.total_amount or 0
            paid = invoice.paid_amount or 0
            remaining = total - paid
            
            self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{total:,.2f}"))
            self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{paid:,.2f}"))
            self.invoices_table.setItem(row, 5, QTableWidgetItem(f"{remaining:,.2f}"))
            
            # الحالة
            if remaining == 0:
                status = "مدفوعة"
                status_color = QColor(39, 174, 96)  # أخضر
            elif paid > 0:
                status = "جزئية"
                status_color = QColor(243, 156, 18)  # برتقالي
            else:
                status = "غير مدفوعة"
                status_color = QColor(231, 76, 60)  # أحمر
            
            status_item = QTableWidgetItem(status)
            status_item.setBackground(status_color)
            status_item.setForeground(QColor(255, 255, 255))
            self.invoices_table.setItem(row, 6, status_item)
            
            # حفظ ID الفاتورة في البيانات
            self.invoices_table.item(row, 0).setData(Qt.UserRole, invoice.id)
    
    def filter_invoices(self):
        """فلترة الفواتير حسب البحث والحالة"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_filter.currentText()
        
        filtered_invoices = []
        
        for invoice in self.all_invoices:
            # فلتر البحث
            if search_text:
                invoice_id_str = f"{invoice.id:06d}"
                with Session(self.engine) as session:
                    customer = session.query(Customer).get(invoice.customer_id) if invoice.customer_id else None
                    customer_name = customer.name if customer else "عميل نقدي"
                
                if (search_text not in invoice_id_str.lower() and 
                    search_text not in customer_name.lower()):
                    continue
            
            # فلتر الحالة
            if status_filter != "جميع الفواتير":
                total = invoice.total_amount or 0
                paid = invoice.paid_amount or 0
                remaining = total - paid
                
                if status_filter == "مدفوعة" and remaining != 0:
                    continue
                elif status_filter == "جزئية" and (remaining == 0 or paid == 0):
                    continue
                elif status_filter == "غير مدفوعة" and paid != 0:
                    continue
            
            filtered_invoices.append(invoice)
        
        self.display_invoices(filtered_invoices)
    
    def select_invoice(self):
        """اختيار الفاتورة المحددة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0:
            invoice_id = self.invoices_table.item(current_row, 0).data(Qt.UserRole)
            
            # التحقق من وجود الفاتورة
            with Session(self.engine) as session:
                invoice = session.query(Transaction).get(invoice_id)
                if invoice:
                    self.selected_invoice = invoice
                    self.accept()
                else:
                    QMessageBox.warning(self, "خطأ", "الفاتورة المحددة غير موجودة")
        else:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار فاتورة أولاً")


class SalesReturnsWidget(QWidget):
    """واجهة مرتجع المبيعات"""
    
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.original_invoice = None
        self.return_items = []
        self.setup_ui()
    
    def setup_ui(self):
        main_layout = QVBoxLayout(self)

        # --- الجداول في QSplitter رأسي ---
        vertical_splitter = QSplitter(Qt.Vertical)

        # --- الجداول في QSplitter أفقي ---
        horizontal_splitter = QSplitter(Qt.Horizontal)

        # يمين: جدول الفاتورة الأصلية + أزرار البحث/إرجاع الكل + زر إخفاء التفاصيل
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        btns_layout = QHBoxLayout()
        search_invoice_btn = QPushButton("🔍 البحث عن فاتورة")
        search_invoice_btn.setStyleSheet("font-size: 15px; font-weight: bold; padding: 8px 18px; background-color: #E74C3C; color: white; border-radius: 8px;")
        search_invoice_btn.clicked.connect(self.search_invoice)
        btns_layout.addWidget(search_invoice_btn)
        return_all_btn = QPushButton("🔄 إرجاع الفاتورة كاملة")
        return_all_btn.setStyleSheet("font-size: 15px; font-weight: bold; padding: 8px 18px; background-color: #E67E22; color: white; border-radius: 8px;")
        return_all_btn.clicked.connect(self.return_full_invoice)
        return_all_btn.setEnabled(False)
        self.return_all_btn = return_all_btn
        btns_layout.addWidget(return_all_btn)
        # زر إخفاء التفاصيل
        self.toggle_details_btn = QPushButton("إخفاء التفاصيل")
        self.toggle_details_btn.setCheckable(True)
        self.toggle_details_btn.setChecked(True)
        self.toggle_details_btn.setStyleSheet("font-size: 14px; padding: 6px 18px; background-color: #34495E; color: white; border-radius: 8px;")
        self.toggle_details_btn.clicked.connect(self.toggle_details_visibility)
        btns_layout.addWidget(self.toggle_details_btn)
        btns_layout.addStretch()
        right_layout.addLayout(btns_layout)
        invoice_table_label = QLabel("الفاتورة الأصلية")
        invoice_table_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #fff; background: #3498DB; border-radius: 8px; padding: 8px 0 8px 0; margin: 0 0 4px 0; text-align: center;")
        right_layout.addWidget(invoice_table_label)
        self.original_items_table = QTableWidget()
        self.original_items_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        right_layout.addWidget(self.original_items_table)
        horizontal_splitter.addWidget(right_widget)

        # يسار: جدول المرتجع
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(2)
        return_table_label = QLabel("المرتجع من الفاتورة")
        return_table_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #fff; background: #E67E22; border-radius: 8px; padding: 8px 0 8px 0; margin: 0 0 4px 0; text-align: center;")
        left_layout.addWidget(return_table_label)
        self.return_items_table = QTableWidget()
        self.return_items_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        left_layout.addWidget(self.return_items_table)
        horizontal_splitter.addWidget(left_widget)

        # اجعل الجداول متساوية في العرض
        horizontal_splitter.setStretchFactor(0, 1)
        horizontal_splitter.setStretchFactor(1, 1)
        vertical_splitter.addWidget(horizontal_splitter)

        # --- الجزء السفلي: تفاصيل الفاتورة + ملخص المرتجع + أزرار الحفظ والطباعة ---
        self.details_frame = QFrame()
        details_layout = QHBoxLayout(self.details_frame)
        details_layout.setContentsMargins(10, 10, 10, 10)
        details_layout.setSpacing(20)
        # يمين: معلومات الفاتورة فقط (بدون ملخص المرتجع)
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(0)
        info_layout.addWidget(self.create_original_invoice_frame(include_summary=False))
        details_layout.addWidget(info_widget, 2)
        # يسار: ملخص المرتجع بالأعلى ثم أزرار الحفظ والطباعة تحته
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(10)
        control_layout.addWidget(self.create_return_summary_frame())
        self.create_control_buttons(control_layout)
        details_layout.addWidget(control_widget, 1)
        vertical_splitter.addWidget(self.details_frame)

        # اجعل الجداول تأخذ المساحة الأكبر
        vertical_splitter.setStretchFactor(0, 3)
        vertical_splitter.setStretchFactor(1, 1)
        main_layout.addWidget(vertical_splitter, 1)
        self.setLayout(main_layout)

    def create_original_invoice_frame(self, include_summary=True):
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
            }
        """)
        layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("📋 معلومات فاتورة المبيعات الأصلية")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                padding: 10px;
                background-color: #ECF0F1;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(section_title)
        
        # معلومات الفاتورة
        info_layout = QFormLayout()
        
        self.invoice_number_label = QLabel("لم يتم اختيار فاتورة")
        self.invoice_date_label = QLabel("-")
        self.customer_name_label = QLabel("-")
        self.invoice_total_label = QLabel("-")
        self.invoice_paid_label = QLabel("-")
        self.invoice_remaining_label = QLabel("-")
        
        # تنسيق التسميات
        for label in [self.invoice_number_label, self.invoice_date_label, 
                     self.customer_name_label, self.invoice_total_label,
                     self.invoice_paid_label, self.invoice_remaining_label]:
            label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    padding: 8px;
                    background-color: white;
                    border: 1px solid #BDC3C7;
                    border-radius: 4px;
                }
            """)
        
        info_layout.addRow("🏷️ رقم الفاتورة:", self.invoice_number_label)
        info_layout.addRow("📅 التاريخ:", self.invoice_date_label)
        info_layout.addRow("👤 العميل:", self.customer_name_label)
        info_layout.addRow("💰 الإجمالي:", self.invoice_total_label)
        info_layout.addRow("💳 المدفوع:", self.invoice_paid_label)
        info_layout.addRow("⏳ المتبقي:", self.invoice_remaining_label)
        
        layout.addLayout(info_layout)

        # ملخص المرتجع فقط إذا كان مطلوبًا
        if include_summary:
            layout.addWidget(self.create_return_summary_frame())
        layout.addStretch()
        
        frame.setLayout(layout)
        return frame

    def create_return_summary_frame(self):
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #FADBD8;
                border: 2px solid #E74C3C;
                border-radius: 8px;
                padding: 15px;
                margin-top: 15px;
            }
        """)
        summary_layout = QGridLayout()
        summary_layout.addWidget(QLabel("💰 إجمالي المرتجع:"), 0, 0)
        self.return_total_label = QLabel("0.00 جنيه")
        self.return_total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #E74C3C;")
        summary_layout.addWidget(self.return_total_label, 0, 1)
        summary_layout.addWidget(QLabel("💳 المبلغ المسترد:"), 1, 0)
        self.refund_amount_spinbox = QDoubleSpinBox()
        self.refund_amount_spinbox.setRange(0, 999999)
        self.refund_amount_spinbox.setDecimals(2)
        self.refund_amount_spinbox.setSuffix(" جنيه")
        self.refund_amount_spinbox.setStyleSheet("font-size: 14px; padding: 8px; border: 2px solid #E74C3C; border-radius: 5px; background-color: white;")
        summary_layout.addWidget(self.refund_amount_spinbox, 1, 1)
        frame.setLayout(summary_layout)
        return frame

    def create_return_items_frame(self):
        """إنشاء إطار منتجات المرتجع"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }
        """)
        layout = QVBoxLayout()

        # عنوان القسم
        section_title = QLabel("📦 منتجات المرتجع")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
                padding: 10px;
                background-color: #ECF0F1;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(section_title)

        # جدول المنتجات الأصلية
        original_label = QLabel("📋 المنتجات في الفاتورة الأصلية:")
        original_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #34495E; margin-bottom: 10px;")
        layout.addWidget(original_label)

        self.original_items_table = QTableWidget()
        self.original_items_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.original_items_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                gridline-color: #BDC3C7;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #3498DB;
                color: white;
                padding: 8px;
                border: 1px solid #2980B9;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        self.original_items_table.setColumnCount(6)
        self.original_items_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية الأصلية", "السعر", "الإجمالي", "كمية المرتجع", "إرجاع"
        ])
        layout.addWidget(self.original_items_table)

        # جدول منتجات المرتجع
        return_label = QLabel("🔄 المنتجات المراد إرجاعها:")
        return_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #E74C3C; margin: 15px 0 10px 0;")
        layout.addWidget(return_label)

        self.return_items_table = QTableWidget()
        self.return_items_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.return_items_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #BDC3C7;
                border-radius: 5px;
                background-color: white;
                gridline-color: #BDC3C7;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #E74C3C;
                color: white;
                padding: 8px;
                border: 1px solid #C0392B;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        self.return_items_table.setColumnCount(5)
        self.return_items_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية", "السعر", "الإجمالي", "حذف"
        ])
        layout.addWidget(self.return_items_table)

        frame.setLayout(layout)
        return frame

    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #ECF0F1;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        buttons_layout = QHBoxLayout()

        # زر حفظ المرتجع
        save_btn = QPushButton("💾 حفظ المرتجع")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                color: #7F8C8D;
            }
        """)
        save_btn.clicked.connect(self.save_return)
        save_btn.setEnabled(False)
        self.save_btn = save_btn
        buttons_layout.addWidget(save_btn)

        # زر حفظ وطباعة
        save_print_btn = QPushButton("🖨️ حفظ وطباعة")
        save_print_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
            QPushButton:disabled {
                background-color: #BDC3C7;
                color: #7F8C8D;
            }
        """)
        save_print_btn.clicked.connect(self.save_and_print_return)
        save_print_btn.setEnabled(False)
        self.save_print_btn = save_print_btn
        buttons_layout.addWidget(save_print_btn)

        buttons_layout.addStretch()

        # زر مسح الكل
        clear_btn = QPushButton("🗑️ مسح الكل")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #E67E22;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                min-width: 180px;
            }
            QPushButton:hover {
                background-color: #D35400;
            }
        """)
        clear_btn.clicked.connect(self.clear_all)
        buttons_layout.addWidget(clear_btn)

        buttons_frame.setLayout(buttons_layout)
        layout.addWidget(buttons_frame)

    def search_invoice(self):
        """البحث عن فاتورة للمرتجع"""
        dialog = InvoiceSearchDialog(self.engine)
        if dialog.exec_() == QDialog.Accepted and dialog.selected_invoice:
            self.load_original_invoice(dialog.selected_invoice)

    def load_original_invoice(self, invoice):
        """تحميل بيانات الفاتورة الأصلية"""
        self.original_invoice = invoice

        # تحديث معلومات الفاتورة
        self.invoice_number_label.setText(f"{invoice.id:06d}")
        self.invoice_date_label.setText(invoice.date.strftime("%Y-%m-%d") if invoice.date else "-")

        # تحميل اسم العميل
        with Session(self.engine) as session:
            customer = session.query(Customer).get(invoice.customer_id) if invoice.customer_id else None
            customer_name = customer.name if customer else "عميل نقدي"
        self.customer_name_label.setText(customer_name)

        # تحديث المبالغ
        total = invoice.total_amount or 0
        paid = invoice.paid_amount or 0
        remaining = total - paid

        self.invoice_total_label.setText(f"{total:,.0f} جنيه")
        self.invoice_paid_label.setText(f"{paid:,.0f} جنيه")
        self.invoice_remaining_label.setText(f"{remaining:,.0f} جنيه")

        # تحميل منتجات الفاتورة
        self.load_original_items()

        # تفعيل زر إرجاع الفاتورة كاملة
        self.return_all_btn.setEnabled(True)

    def load_original_items(self):
        """تحميل منتجات الفاتورة الأصلية"""
        if not self.original_invoice:
            return

        try:
            with Session(self.engine) as session:
                items = session.query(TransactionItem, Product).join(Product).filter(
                    TransactionItem.transaction_id == self.original_invoice.id
                ).all()

                self.original_items_table.setRowCount(0)
                self.original_items_table.setColumnCount(6)
                self.original_items_table.setHorizontalHeaderLabels([
                    "المنتج", "الكمية الأصلية", "السعر", "الإجمالي", "كمية المرتجع", "إرجاع"
                ])
                self.original_items_table.setRowCount(len(items))

                for row, (item, product) in enumerate(items):
                    # اسم المنتج
                    self.original_items_table.setItem(row, 0, QTableWidgetItem(product.name))

                    # الكمية الأصلية
                    self.original_items_table.setItem(row, 1, QTableWidgetItem(str(item.quantity)))

                    # السعر
                    self.original_items_table.setItem(row, 2, QTableWidgetItem(f"{item.price:,.2f}"))

                    # الإجمالي
                    total = item.quantity * item.price
                    self.original_items_table.setItem(row, 3, QTableWidgetItem(f"{total:,.2f}"))

                    # حقل كمية المرتجع
                    return_qty_spinbox = QSpinBox()
                    return_qty_spinbox.setRange(0, item.quantity)
                    return_qty_spinbox.setStyleSheet("""
                        QSpinBox {
                            font-size: 12px;
                            padding: 4px;
                            border: 1px solid #BDC3C7;
                            border-radius: 3px;
                        }
                    """)
                    self.original_items_table.setCellWidget(row, 4, return_qty_spinbox)

                    # زر إضافة للمرتجع
                    add_btn = QPushButton("➕")
                    add_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #E74C3C;
                            color: white;
                            font-weight: bold;
                            border: none;
                            border-radius: 3px;
                            padding: 4px;
                            max-width: 30px;
                            max-height: 25px;
                        }
                        QPushButton:hover {
                            background-color: #C0392B;
                        }
                    """)
                    add_btn.clicked.connect(lambda checked, r=row: self.add_to_return(r))
                    self.original_items_table.setCellWidget(row, 5, add_btn)

                # تقسيم الأعمدة تلقائيًا
                header = self.original_items_table.horizontalHeader()
                header.setSectionResizeMode(0, QHeaderView.Stretch)   # المنتج
                header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الكمية الأصلية
                header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # السعر
                header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الإجمالي
                header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # كمية المرتجع
                header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # إرجاع

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل منتجات الفاتورة:\n{str(e)}")

    def add_to_return(self, row):
        """إضافة منتج للمرتجع"""
        try:
            # الحصول على كمية المرتجع
            return_qty_widget = self.original_items_table.cellWidget(row, 4)
            return_qty = return_qty_widget.value()

            if return_qty <= 0:
                QMessageBox.warning(self, "تنبيه", "يرجى تحديد كمية المرتجع")
                return

            # الحصول على بيانات المنتج
            product_name = self.original_items_table.item(row, 0).text()
            original_qty = int(self.original_items_table.item(row, 1).text())
            price = float(self.original_items_table.item(row, 2).text().replace(',', ''))

            # التحقق من عدم تجاوز الكمية الأصلية
            if return_qty > original_qty:
                QMessageBox.warning(self, "خطأ", "كمية المرتجع لا يمكن أن تكون أكبر من الكمية الأصلية")
                return

            # البحث عن المنتج في قاعدة البيانات
            with Session(self.engine) as session:
                items = session.query(TransactionItem, Product).join(Product).filter(
                    TransactionItem.transaction_id == self.original_invoice.id
                ).all()

                if row < len(items):
                    item, product = items[row]

                    # إضافة للمرتجع
                    return_item = {
                        'product_id': product.id,
                        'product_name': product.name,
                        'quantity': return_qty,
                        'price': price,
                        'total': return_qty * price
                    }

                    # التحقق من عدم وجود المنتج مسبقاً في المرتجع
                    existing_item = None
                    for i, existing in enumerate(self.return_items):
                        if existing['product_id'] == product.id:
                            existing_item = i
                            break

                    if existing_item is not None:
                        # تحديث الكمية
                        self.return_items[existing_item]['quantity'] += return_qty
                        self.return_items[existing_item]['total'] = (
                            self.return_items[existing_item]['quantity'] *
                            self.return_items[existing_item]['price']
                        )
                    else:
                        # إضافة منتج جديد
                        self.return_items.append(return_item)

                    # تحديث الجدول والملخص
                    self.update_return_items_table()
                    self.update_return_summary()

                    # إعادة تعيين كمية المرتجع
                    return_qty_widget.setValue(0)

                    # تفعيل أزرار الحفظ
                    self.save_btn.setEnabled(True)
                    self.save_print_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة المنتج للمرتجع:\n{str(e)}")

    def update_return_items_table(self):
        """تحديث جدول منتجات المرتجع"""
        self.return_items_table.setRowCount(len(self.return_items))
        self.return_items_table.setColumnCount(5)
        self.return_items_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية", "السعر", "الإجمالي", "حذف"
        ])
        for row, item in enumerate(self.return_items):
            # اسم المنتج
            product_item = QTableWidgetItem(item['product_name'])
            product_item.setTextAlignment(Qt.AlignCenter)
            self.return_items_table.setItem(row, 0, product_item)

            # الكمية
            qty_item = QTableWidgetItem(str(item['quantity']))
            qty_item.setTextAlignment(Qt.AlignCenter)
            self.return_items_table.setItem(row, 1, qty_item)

            # السعر
            price_item = QTableWidgetItem(f"{item['price']:,.2f}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.return_items_table.setItem(row, 2, price_item)

            # الإجمالي
            total_item = QTableWidgetItem(f"{item['total']:,.2f}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.return_items_table.setItem(row, 3, total_item)

            # زر الحذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #E74C3C;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 3px;
                    padding: 4px;
                    max-width: 30px;
                    max-height: 25px;
                }
                QPushButton:hover {
                    background-color: #C0392B;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_return_item(r))
            self.return_items_table.setCellWidget(row, 4, delete_btn)

        # تقسيم الأعمدة تلقائيًا
        header = self.return_items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)   # المنتج
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # السعر
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # حذف
        self.return_items_table.update()
        self.return_items_table.repaint()

    def remove_return_item(self, row):
        """حذف منتج من المرتجع"""
        if 0 <= row < len(self.return_items):
            self.return_items.pop(row)
            self.update_return_items_table()
            self.update_return_summary()

            # تعطيل أزرار الحفظ إذا لم تعد هناك منتجات
            if not self.return_items:
                self.save_btn.setEnabled(False)
                self.save_print_btn.setEnabled(False)

    def calculate_proportional_discount(self, item_total):
        """حساب الخصم النسبي للمنتج بناءً على الفاتورة الأصلية"""
        if not self.original_invoice or not self.original_invoice.discount:
            return 0

        try:
            # حساب المجموع الفرعي للفاتورة الأصلية
            with Session(self.engine) as session:
                original_items = session.query(TransactionItem).filter(
                    TransactionItem.transaction_id == self.original_invoice.id
                ).all()

                original_subtotal = sum(item.quantity * item.price for item in original_items)

                if original_subtotal <= 0:
                    return 0

                # حساب نسبة الخصم من الفاتورة الأصلية
                discount_percentage = (self.original_invoice.discount / original_subtotal) * 100

                # تطبيق نفس النسبة على المنتج المرتجع
                item_discount = item_total * (discount_percentage / 100)

                return item_discount

        except Exception as e:
            print(f"خطأ في حساب الخصم النسبي: {e}")
            return 0

    def update_return_summary(self):
        """تحديث ملخص المرتجع مع تطبيق الخصم النسبي"""
        total_return_before_discount = sum(item['total'] for item in self.return_items)
        total_discount = 0

        # حساب الخصم النسبي لكل منتج
        for item in self.return_items:
            item_discount = self.calculate_proportional_discount(item['total'])
            total_discount += item_discount

        # المبلغ النهائي بعد الخصم
        total_return_after_discount = total_return_before_discount - total_discount

        # عرض المعلومات
        if total_discount > 0:
            self.return_total_label.setText(
                f"{total_return_before_discount:,.0f} جنيه\n"
                f"الخصم: -{total_discount:,.0f} جنيه\n"
                f"النهائي: {total_return_after_discount:,.0f} جنيه"
            )
        else:
            self.return_total_label.setText(f"{total_return_before_discount:,.0f} جنيه")

        # تحديث مبلغ الاسترداد
        self.refund_amount_spinbox.setValue(total_return_after_discount)

    def save_return(self):
        """حفظ المرتجع"""
        if not self.original_invoice or not self.return_items:
            QMessageBox.warning(self, "تنبيه", "لا توجد منتجات للمرتجع")
            return None

        try:
            with Session(self.engine) as session:
                # حساب المبالغ مع الخصم النسبي
                total_before_discount = sum(item['total'] for item in self.return_items)
                total_discount = sum(self.calculate_proportional_discount(item['total']) for item in self.return_items)
                total_after_discount = total_before_discount - total_discount

                # إنشاء فاتورة المرتجع
                return_transaction = Transaction(
                    type=TransactionType.SALE_RETURN,
                    total_amount=total_after_discount,  # المبلغ النهائي بعد الخصم
                    paid_amount=self.refund_amount_spinbox.value(),
                    discount=total_discount,  # حفظ مبلغ الخصم
                    customer_id=self.original_invoice.customer_id,
                    original_transaction_id=self.original_invoice.id,
                    date=datetime.now(),
                    notes=f"مرتجع للفاتورة رقم {self.original_invoice.id:06d}"
                )
                session.add(return_transaction)

                # إضافة منتجات المرتجع
                for item in self.return_items:
                    return_item = TransactionItem(
                        transaction=return_transaction,
                        product_id=item['product_id'],
                        quantity=item['quantity'],
                        price=item['price']
                    )
                    session.add(return_item)

                    # إعادة المنتجات للمخزون
                    product = session.query(Product).get(item['product_id'])
                    if product:
                        product.quantity += item['quantity']

                # تحديث رصيد العميل (خصم المبلغ المسترد)
                if self.original_invoice.customer_id:
                    customer = session.query(Customer).get(self.original_invoice.customer_id)
                    if customer:
                        customer.balance -= self.refund_amount_spinbox.value()

                session.commit()

                # حفظ ID المرتجع للإرجاع
                return_id = return_transaction.id

                QMessageBox.information(self, "نجاح",
                    f"تم حفظ المرتجع بنجاح!\n"
                    f"رقم المرتجع: {return_id:06d}\n"
                    f"المبلغ المسترد: {self.refund_amount_spinbox.value():,.0f} جنيه")

                self.clear_all()
                return return_id

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المرتجع:\n{str(e)}")
            return None

    def save_and_print_return(self):
        """حفظ وطباعة المرتجع"""
        return_id = self.save_return()
        if return_id:
            try:
                # طباعة فاتورة المرتجع
                from utils.advanced_invoice_printer import show_advanced_print_dialog
                show_advanced_print_dialog(self.engine, return_id, self)
            except Exception as e:
                QMessageBox.critical(self, "خطأ في الطباعة",
                    f"تم حفظ المرتجع بنجاح ولكن حدث خطأ أثناء الطباعة:\n{str(e)}")

    def clear_all(self):
        """مسح جميع البيانات"""
        self.original_invoice = None
        self.return_items = []

        # مسح معلومات الفاتورة
        self.invoice_number_label.setText("لم يتم اختيار فاتورة")
        self.invoice_date_label.setText("-")
        self.customer_name_label.setText("-")
        self.invoice_total_label.setText("-")
        self.invoice_paid_label.setText("-")
        self.invoice_remaining_label.setText("-")

        # مسح الجداول
        self.original_items_table.setRowCount(0)
        self.return_items_table.setRowCount(0)

        # مسح الملخص
        self.return_total_label.setText("0.00 جنيه")
        self.refund_amount_spinbox.setValue(0)

        # تعطيل أزرار الحفظ
        self.save_btn.setEnabled(False)
        self.save_print_btn.setEnabled(False)

        # تعطيل زر إرجاع الفاتورة كاملة
        self.return_all_btn.setEnabled(False)

    def return_full_invoice(self):
        """إرجاع الفاتورة كاملة بضغطة واحدة"""
        if not self.original_invoice:
            QMessageBox.warning(self, "تنبيه", "لا توجد فاتورة محددة")
            return

        # رسالة تأكيد
        reply = QMessageBox.question(self, "تأكيد الإرجاع",
            f"هل تريد إرجاع الفاتورة رقم {self.original_invoice.id:06d} كاملة؟\n"
            f"سيتم إرجاع جميع المنتجات بكامل كمياتها.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        try:
            # مسح المرتجعات الحالية
            self.return_items = []

            # إضافة جميع منتجات الفاتورة للمرتجع
            with Session(self.engine) as session:
                items = session.query(TransactionItem, Product).join(Product).filter(
                    TransactionItem.transaction_id == self.original_invoice.id
                ).all()

                for item, product in items:
                    return_item = {
                        'product_id': product.id,
                        'product_name': product.name,
                        'quantity': item.quantity,
                        'price': item.price,
                        'total': item.quantity * item.price
                    }
                    self.return_items.append(return_item)

                # تحديث الجداول والملخص
                self.update_return_items_table()
                self.update_return_summary()

                # تفعيل أزرار الحفظ
                self.save_btn.setEnabled(True)
                self.save_print_btn.setEnabled(True)

                QMessageBox.information(self, "نجاح",
                    f"تم إضافة جميع منتجات الفاتورة للمرتجع!\n"
                    f"عدد المنتجات: {len(self.return_items)}\n"
                    f"إجمالي المرتجع: {sum(item['total'] for item in self.return_items):,.0f} جنيه")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إرجاع الفاتورة كاملة:\n{str(e)}")

    def toggle_details_visibility(self):
        visible = self.toggle_details_btn.isChecked()
        self.details_frame.setVisible(visible)
        self.toggle_details_btn.setText("إخفاء التفاصيل" if visible else "إظهار التفاصيل")
