#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة بيانات للأدوات المنزلية من الشركات المصرية المعروفة
"""

import pandas as pd
import random
from datetime import datetime

def create_egyptian_home_products():
    """إنشاء قاعدة بيانات شاملة للأدوات المنزلية المصرية"""
    
    # الشركات المصرية المعروفة
    companies = {
        "أحد": "AHAD",
        "فريش": "FRESH", 
        "توشيبا العربي": "TOSHIBA",
        "يونيون إير": "UNION_AIR",
        "كريازي": "KIRIAZI",
        "زانوسي": "ZANUSSI",
        "إنديست": "INDESIT",
        "أوليمبيك": "OLYMPIC",
        "وايت ويل": "WHITE_WHALE",
        "بيكو": "BEKO",
        "شارب العربي": "SHARP",
        "هيتاشي": "HITACHI",
        "إل جي": "LG",
        "سامسونج": "SAMSUNG",
        "الأسكا": "ALASKA"
    }
    
    # فئات المنتجات
    categories = {
        "أجهزة كهربائية كبيرة": [
            "ثلاجة", "غسالة", "غسالة أطباق", "فريزر", "مكيف هواء", "سخان مياه"
        ],
        "أجهزة كهربائية صغيرة": [
            "مكواة", "مجفف شعر", "خلاط", "محضر طعام", "مايكروويف", "فرن كهربائي",
            "غلاية كهربائية", "محمصة خبز", "مقلاة هوائية", "عصارة"
        ],
        "أدوات مطبخ": [
            "طقم أواني", "طقم سكاكين", "لوح تقطيع", "مصفاة", "مقلاة", "حلة ضغط",
            "طقم تقديم", "علب حفظ", "ترمس", "إبريق شاي"
        ],
        "أدوات تنظيف": [
            "مكنسة كهربائية", "ممسحة", "دلو", "فرشاة", "منظفات", "مساحيق غسيل",
            "معطر جو", "إسفنجة", "قفازات", "كيس قمامة"
        ],
        "أدوات حمام": [
            "دش", "خلاط مياه", "مرآة", "رف حمام", "ستارة حمام", "سجادة حمام",
            "موزع صابون", "فرشاة أسنان", "منشفة", "سلة غسيل"
        ],
        "إضاءة وكهرباء": [
            "لمبة LED", "نجفة", "أباجورة", "كشاف", "مفتاح كهرباء", "مقبس كهرباء",
            "سلك كهرباء", "لمبة توفير", "بطارية", "شاحن"
        ]
    }
    
    products = []
    product_id = 1
    
    for category, items in categories.items():
        for item in items:
            for company_ar, company_en in companies.items():
                # إنشاء منتجات متنوعة لكل شركة
                models = ["موديل A", "موديل B", "موديل C", "الفاخر", "العادي", "المطور"]
                
                for i, model in enumerate(models[:random.randint(2, 4)]):
                    product_name = f"{item} {company_ar} {model}"
                    product_code = f"{company_en}_{category[:3].upper()}_{product_id:04d}"
                    
                    # تحديد الأسعار حسب الفئة والشركة
                    base_prices = {
                        "أجهزة كهربائية كبيرة": (8000, 25000),
                        "أجهزة كهربائية صغيرة": (200, 3000),
                        "أدوات مطبخ": (50, 1500),
                        "أدوات تنظيف": (25, 800),
                        "أدوات حمام": (30, 1200),
                        "إضاءة وكهرباء": (15, 500)
                    }
                    
                    min_price, max_price = base_prices[category]
                    purchase_price = random.randint(min_price, max_price)
                    sale_price = int(purchase_price * random.uniform(1.2, 1.8))
                    
                    # الكمية والحد الأدنى
                    quantity = random.randint(5, 100)
                    min_quantity = random.randint(2, 10)
                    
                    # الوحدة
                    units = ["قطعة", "عبوة", "طقم", "زوج", "متر", "كيلو"]
                    unit = random.choice(units)
                    
                    # الباركود
                    barcode = f"629{random.randint(*********, *********)}"
                    
                    # الوصف
                    descriptions = [
                        f"منتج عالي الجودة من شركة {company_ar}",
                        f"تصميم عصري ومتين من {company_ar}",
                        f"منتج موثوق وعملي من {company_ar}",
                        f"جودة ممتازة وضمان شامل من {company_ar}",
                        f"تقنية متطورة من {company_ar}"
                    ]
                    
                    product = {
                        "الكود": product_code,
                        "اسم المنتج": product_name,
                        "الوصف": random.choice(descriptions),
                        "الفئة": category,
                        "الشركة": company_ar,
                        "سعر الشراء": purchase_price,
                        "سعر البيع": sale_price,
                        "الكمية": quantity,
                        "الحد الأدنى": min_quantity,
                        "الوحدة": unit,
                        "الباركود": barcode,
                        "نشط": "نعم"
                    }
                    
                    products.append(product)
                    product_id += 1
                    
                    # توقف عند 300 منتج لتجنب الملف الكبير
                    if len(products) >= 300:
                        break
                        
                if len(products) >= 300:
                    break
            if len(products) >= 300:
                break
        if len(products) >= 300:
            break
    
    return products

def save_to_excel():
    """حفظ البيانات في ملف Excel"""
    products = create_egyptian_home_products()
    df = pd.DataFrame(products)
    
    # حفظ الملف
    filename = "sample_data/منتجات_الأدوات_المنزلية_المصرية.xlsx"
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"تم إنشاء {len(products)} منتج في الملف: {filename}")
    print(f"الشركات المتضمنة: {len(set(df['الشركة']))} شركة")
    print(f"الفئات المتضمنة: {len(set(df['الفئة']))} فئة")
    
    return filename, len(products)

def save_to_csv():
    """حفظ البيانات في ملف CSV"""
    products = create_egyptian_home_products()
    df = pd.DataFrame(products)
    
    # حفظ الملف
    filename = "sample_data/منتجات_الأدوات_المنزلية_المصرية.csv"
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    
    return filename, len(products)

if __name__ == "__main__":
    # إنشاء مجلد البيانات إذا لم يكن موجود
    import os
    os.makedirs("sample_data", exist_ok=True)
    
    # إنشاء الملفات
    excel_file, count = save_to_excel()
    csv_file, _ = save_to_csv()
    
    print(f"\n✅ تم إنشاء قاعدة البيانات بنجاح!")
    print(f"📁 ملف Excel: {excel_file}")
    print(f"📁 ملف CSV: {csv_file}")
    print(f"📊 عدد المنتجات: {count}")
