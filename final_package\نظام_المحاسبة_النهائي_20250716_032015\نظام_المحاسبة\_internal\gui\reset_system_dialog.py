"""
نافذة إعادة ضبط النظام - للمدير فقط
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QLineEdit, QMessageBox, QFrame, QGroupBox,
                             QCheckBox, QTextEdit, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
import shutil
from datetime import datetime


class ResetWorker(QThread):
    """عامل منفصل لتنفيذ عملية إعادة الضبط"""
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, engine, reset_options):
        super().__init__()
        self.engine = engine
        self.reset_options = reset_options
    
    def run(self):
        try:
            Session = sessionmaker(bind=self.engine)
            # استخدم context manager للجلسة
            with Session() as session:
                # إنشاء نسخة احتياطية قبل الإعادة ضبط
                self.progress.emit("إنشاء نسخة احتياطية...")
                backup_path = f"backup_before_reset_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                shutil.copy2("accounting.db", backup_path)
                
                if self.reset_options.get('reset_invoices', False):
                    self.progress.emit("حذف جميع الفواتير...")
                    session.execute(text("DELETE FROM transactions"))
                    session.execute(text("DELETE FROM transaction_items"))
                
                if self.reset_options.get('reset_products', False):
                    self.progress.emit("حذف جميع المنتجات...")
                    session.execute(text("DELETE FROM products"))
                
                if self.reset_options.get('reset_inventory', False):
                    self.progress.emit("إعادة تعيين المخزون...")
                    session.execute(text("UPDATE products SET quantity = 0"))
                
                if self.reset_options.get('reset_customers', False):
                    self.progress.emit("حذف جميع العملاء...")
                    session.execute(text("DELETE FROM customers"))
                
                if self.reset_options.get('reset_suppliers', False):
                    self.progress.emit("حذف جميع الموردين...")
                    session.execute(text("DELETE FROM suppliers"))
                
                if self.reset_options.get('reset_audit_log', False):
                    self.progress.emit("حذف سجل العمليات...")
                    session.execute(text("DELETE FROM audit_log"))
                
                session.commit()
            
            self.progress.emit("تم إعادة ضبط النظام بنجاح!")
            self.finished.emit(True, f"تم إعادة ضبط النظام بنجاح!\nتم إنشاء نسخة احتياطية: {backup_path}")
            
        except Exception as e:
            self.finished.emit(False, f"حدث خطأ أثناء إعادة ضبط النظام:\n{str(e)}")


class ResetSystemDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔄 إعادة ضبط النظام")
        self.setModal(True)
        self.setMinimumSize(600, 500)
        
        # إضافة أزرار التحكم في النافذة
        self.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)
        
        self.setup_ui()
        self.check_admin_permission()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان النافذة
        title_label = QLabel("🔄 إعادة ضبط النظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)
        
        # تحذير مهم
        warning_label = QLabel("⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!")
        warning_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                background-color: #fdf2f2;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        warning_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(warning_label)
        
        # مجموعة كلمة المرور
        password_group = QGroupBox("🔐 كلمة مرور المدير")
        password_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #3498DB;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        password_layout = QVBoxLayout()
        password_layout.setContentsMargins(20, 20, 20, 20)
        password_layout.setSpacing(15)
        password_group.setLayout(password_layout)
        
        password_label = QLabel("أدخل كلمة مرور المدير:")
        password_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #495057;")
        password_layout.addWidget(password_label)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("كلمة المرور")
        self.password_edit.setStyleSheet("""
            QLineEdit {
                padding: 12px 15px;
                font-size: 16px;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498DB;
            }
        """)
        password_layout.addWidget(self.password_edit)
        
        layout.addWidget(password_group)
        
        # مجموعة خيارات الإعادة ضبط
        options_group = QGroupBox("📋 خيارات الإعادة ضبط")
        options_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2C3E50;
                border: 2px solid #3498DB;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #F8F9FA;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        options_layout = QVBoxLayout()
        options_layout.setContentsMargins(20, 20, 20, 20)
        options_layout.setSpacing(15)
        options_group.setLayout(options_layout)
        
        # خيارات الإعادة ضبط
        self.reset_invoices = QCheckBox("🗑️ حذف جميع الفواتير والمرتجعات")
        self.reset_products = QCheckBox("🗑️ حذف جميع المنتجات")
        self.reset_inventory = QCheckBox("📦 إعادة تعيين المخزون (صفر)")
        self.reset_customers = QCheckBox("👥 حذف جميع العملاء")
        self.reset_suppliers = QCheckBox("🏢 حذف جميع الموردين")
        self.reset_audit_log = QCheckBox("📋 حذف سجل العمليات")
        
        # تنسيق الخيارات
        checkbox_style = """
            QCheckBox {
                font-size: 14px;
                font-weight: bold;
                color: #495057;
                spacing: 10px;
                padding: 8px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #DEE2E6;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #e74c3c;
                border-color: #e74c3c;
            }
        """
        
        self.reset_invoices.setStyleSheet(checkbox_style)
        self.reset_products.setStyleSheet(checkbox_style)
        self.reset_inventory.setStyleSheet(checkbox_style)
        self.reset_customers.setStyleSheet(checkbox_style)
        self.reset_suppliers.setStyleSheet(checkbox_style)
        self.reset_audit_log.setStyleSheet(checkbox_style)
        
        options_layout.addWidget(self.reset_invoices)
        options_layout.addWidget(self.reset_products)
        options_layout.addWidget(self.reset_inventory)
        options_layout.addWidget(self.reset_customers)
        options_layout.addWidget(self.reset_suppliers)
        options_layout.addWidget(self.reset_audit_log)
        
        layout.addWidget(options_group)
        
        # منطقة التقدم
        self.progress_area = QFrame()
        self.progress_area.setVisible(False)
        self.progress_area.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        progress_layout = QVBoxLayout()
        self.progress_area.setLayout(progress_layout)
        
        self.progress_label = QLabel("جاري إعادة ضبط النظام...")
        self.progress_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #495057;")
        progress_layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #DEE2E6;
                border-radius: 8px;
                text-align: center;
                background-color: white;
            }
            QProgressBar::chunk {
                background-color: #3498DB;
                border-radius: 6px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(self.progress_area)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.reset_btn = QPushButton("🔄 إعادة ضبط النظام")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95A5A6;
            }
        """)
        self.reset_btn.clicked.connect(self.start_reset)
        buttons_layout.addWidget(self.reset_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 8px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def check_admin_permission(self):
        """التحقق من صلاحيات المدير"""
        # يمكن إضافة منطق التحقق من الصلاحيات هنا
        pass
    
    def verify_admin_password(self):
        """التحقق من كلمة مرور المدير"""
        password = self.password_edit.text().strip()
        
        # كلمة مرور المدير الافتراضية
        admin_password = "sicoo123"
        
        if password == admin_password:
            return True
        else:
            QMessageBox.warning(self, "خطأ", "كلمة المرور غير صحيحة!")
            return False
    
    def start_reset(self):
        """بدء عملية إعادة الضبط"""
        # التحقق من كلمة المرور
        if not self.verify_admin_password():
            return
        
        # التحقق من اختيار خيار واحد على الأقل
        reset_options = {
            'reset_invoices': self.reset_invoices.isChecked(),
            'reset_products': self.reset_products.isChecked(),
            'reset_inventory': self.reset_inventory.isChecked(),
            'reset_customers': self.reset_customers.isChecked(),
            'reset_suppliers': self.reset_suppliers.isChecked(),
            'reset_audit_log': self.reset_audit_log.isChecked()
        }
        
        if not any(reset_options.values()):
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار خيار واحد على الأقل للإعادة ضبط!")
            return
        
        # تأكيد العملية
        options_text = []
        if reset_options['reset_invoices']:
            options_text.append("• حذف جميع الفواتير والمرتجعات")
        if reset_options['reset_products']:
            options_text.append("• حذف جميع المنتجات")
        if reset_options['reset_inventory']:
            options_text.append("• إعادة تعيين المخزون")
        if reset_options['reset_customers']:
            options_text.append("• حذف جميع العملاء")
        if reset_options['reset_suppliers']:
            options_text.append("• حذف جميع الموردين")
        if reset_options['reset_audit_log']:
            options_text.append("• حذف سجل العمليات")
        
        confirm_text = f"""هل أنت متأكد من إعادة ضبط النظام؟

الخيارات المحددة:
{chr(10).join(options_text)}

⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!
سيتم إنشاء نسخة احتياطية تلقائياً قبل الإعادة ضبط."""
        
        reply = QMessageBox.question(
            self, 
            "تأكيد إعادة ضبط النظام", 
            confirm_text,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.execute_reset(reset_options)
    
    def execute_reset(self, reset_options):
        """تنفيذ عملية الإعادة ضبط"""
        try:
            # إخفاء أزرار التحكم وإظهار منطقة التقدم
            self.reset_btn.setEnabled(False)
            self.cancel_btn.setEnabled(False)
            self.progress_area.setVisible(True)
            
            # إنشاء محرك قاعدة البيانات
            engine = create_engine('sqlite:///accounting.db')
            
            # إنشاء عامل منفصل لتنفيذ العملية
            self.reset_worker = ResetWorker(engine, reset_options)
            self.reset_worker.progress.connect(self.update_progress)
            self.reset_worker.finished.connect(self.reset_finished)
            self.reset_worker.start()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء بدء عملية الإعادة ضبط:\n{str(e)}")
            self.reset_btn.setEnabled(True)
            self.cancel_btn.setEnabled(True)
            self.progress_area.setVisible(False)
    
    def update_progress(self, message):
        """تحديث رسالة التقدم"""
        self.progress_label.setText(message)
    
    def reset_finished(self, success, message):
        """انتهاء عملية الإعادة ضبط"""
        self.progress_area.setVisible(False)
        self.reset_btn.setEnabled(True)
        self.cancel_btn.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "نجح", message)
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", message) 