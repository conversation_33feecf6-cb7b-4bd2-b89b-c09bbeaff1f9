from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QSystemTrayIcon,
                           QMenu, QAction, QPushButton, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt5.QtGui import QIcon
from sqlalchemy.orm import Session
from database.models import Product, Transaction, Customer, TransactionType
from datetime import datetime, timedelta

class NotificationSystem(QObject):
    notification_signal = pyqtSignal(str, str)  # العنوان والرسالة

    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_tray()
        self.start_monitoring()

    def setup_tray(self):
        self.tray_icon = QSystemTrayIcon()
        self.tray_icon.setIcon(QIcon("images/logo.png"))
        
        # إنشاء قائمة النظام
        tray_menu = QMenu()
        show_action = QAction("عرض التنبيهات", self)
        show_action.triggered.connect(self.show_notifications)
        quit_action = QAction("خروج", self)
        quit_action.triggered.connect(self.quit_app)
        
        tray_menu.addAction(show_action)
        tray_menu.addAction(quit_action)
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()

        # ربط إشارة التنبيهات
        self.notification_signal.connect(self.show_notification)

    def start_monitoring(self):
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_alerts)
        self.timer.start(300000)  # التحقق كل 5 دقائق
        self.check_alerts()  # التحقق الفوري عند البدء

    def check_alerts(self):
        with Session(self.engine) as session:
            # التحقق من المخزون المنخفض
            low_stock = session.query(Product).filter(
                Product.quantity <= Product.min_quantity,
                Product.min_quantity > 0  # فقط المنتجات التي لها حد أدنى محدد
            ).all()

            for product in low_stock:
                self.notification_signal.emit(
                    "تنبيه المخزون",
                    f"المنتج {product.name} منخفض المخزون ({product.quantity} متبقي من أصل {product.min_quantity} حد أدنى)"
                )

            # التحقق من المدفوعات المستحقة
            due_date = datetime.now() - timedelta(days=30)
            due_payments = session.query(Transaction).filter(
                Transaction.total_amount > Transaction.paid_amount,
                Transaction.date <= due_date
            ).all()
            
            for payment in due_payments:
                customer_name = payment.customer.name if payment.customer else "غير محدد"
                amount_due = payment.total_amount - payment.paid_amount
                self.notification_signal.emit(
                    "دفعة مستحقة",
                    f"دفعة مستحقة من {customer_name} - المبلغ: {amount_due:,.0f} ج.م"
                )

            # التحقق من صلاحية المنتجات (إذا كان هناك تاريخ صلاحية)
            if hasattr(Product, 'expiry_date'):
                expiring_soon = session.query(Product).filter(
                    Product.expiry_date <= datetime.now() + timedelta(days=30)
                ).all()
                
                for product in expiring_soon:
                    days_left = (product.expiry_date - datetime.now()).days
                    self.notification_signal.emit(
                        "تنبيه الصلاحية",
                        f"المنتج {product.name} سينتهي خلال {days_left} يوم"
                    )

    def show_notification(self, title, message):
        self.tray_icon.showMessage(
            title,
            message,
            QSystemTrayIcon.Information,
            5000  # عرض لمدة 5 ثواني
        )

    def show_notifications(self):
        self.notifications_window = NotificationsWindow(self.engine)
        self.notifications_window.show()

    def show_settings(self):
        """عرض نافذة إعدادات التنبيهات"""
        try:
            from .notifications_settings import NotificationsSettingsDialog
            settings_dialog = NotificationsSettingsDialog(self.engine)
            settings_dialog.exec_()
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "خطأ", f"حدث خطأ في إعدادات التنبيهات:\n{str(e)}")

    def quit_app(self):
        self.timer.stop()
        from PyQt5.QtWidgets import QApplication
        QApplication.quit()

class NotificationsWindow(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("التنبيهات")
        self.setMinimumSize(400, 600)
        self.setStyleSheet("""
            QWidget {
                background-color: white;
            }
            QLabel.notification {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                padding: 10px;
                margin: 5px;
            }
            QLabel.notification:hover {
                background-color: #E9ECEF;
            }
            QPushButton {
                background-color: #007BFF;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # عنوان النافذة
        title = QLabel("التنبيهات والإشعارات")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # منطقة التمرير للتنبيهات
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll_content = QWidget()
        self.notifications_layout = QVBoxLayout(scroll_content)
        scroll.setWidget(scroll_content)
        layout.addWidget(scroll)
        
        # زر تحديث التنبيهات
        refresh_btn = QPushButton("تحديث التنبيهات")
        refresh_btn.clicked.connect(self.load_notifications)
        layout.addWidget(refresh_btn)
        
        self.load_notifications()
        
    def load_notifications(self):
        # مسح التنبيهات القديمة
        for i in reversed(range(self.notifications_layout.count())):
            self.notifications_layout.itemAt(i).widget().deleteLater()
            
        with Session(self.engine) as session:
            # تحميل تنبيهات المخزون
            low_stock = session.query(Product).filter(
                Product.quantity <= Product.min_quantity,
                Product.min_quantity > 0  # فقط المنتجات التي لها حد أدنى محدد
            ).all()

            for product in low_stock:
                self.add_notification(
                    "تنبيه المخزون",
                    f"المنتج {product.name} منخفض المخزون ({product.quantity} متبقي من أصل {product.min_quantity} حد أدنى)",
                    "#FFF3CD"  # لون أصفر فاتح للتحذير
                )
            
            # تحميل تنبيهات المدفوعات المستحقة
            due_payments = session.query(Transaction).filter(
                Transaction.total_amount > Transaction.paid_amount,
                Transaction.date <= datetime.now() - timedelta(days=30)
            ).all()
            
            for payment in due_payments:
                customer_name = payment.customer.name if payment.customer else "غير محدد"
                amount_due = payment.total_amount - payment.paid_amount
                self.add_notification(
                    "دفعة مستحقة",
                    f"دفعة مستحقة من {customer_name} - المبلغ: {amount_due:,.2f}",
                    "#FFE5E5"  # لون أحمر فاتح للمدفوعات المتأخرة
                )
                
    def add_notification(self, title, message, bg_color):
        notification = QLabel(f"<b>{title}</b><br>{message}")
        notification.setProperty("class", "notification")
        notification.setStyleSheet(f"""
            QLabel.notification {{
                background-color: {bg_color};
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                padding: 10px;
                margin: 5px;
            }}
        """)
        notification.setWordWrap(True)
        self.notifications_layout.addWidget(notification)


class NotificationsWidget(QWidget):
    """واجهة عرض التنبيهات في تبويب منفصل"""

    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        self.load_notifications()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان الصفحة
        title_label = QLabel("التنبيهات والإشعارات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # منطقة التنبيهات
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)

        self.notifications_widget = QWidget()
        self.notifications_layout = QVBoxLayout(self.notifications_widget)
        self.notifications_layout.setSpacing(10)
        self.notifications_layout.setContentsMargins(15, 15, 15, 15)

        scroll_area.setWidget(self.notifications_widget)
        layout.addWidget(scroll_area)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث التنبيهات")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #27ae60, stop:1 #2ecc71);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                           stop:0 #229954, stop:1 #27ae60);
            }
        """)
        refresh_btn.clicked.connect(self.load_notifications)
        layout.addWidget(refresh_btn)

    def load_notifications(self):
        """تحميل التنبيهات"""
        # مسح التنبيهات الموجودة
        for i in reversed(range(self.notifications_layout.count())):
            child = self.notifications_layout.itemAt(i).widget()
            if child:
                child.deleteLater()

        try:
            from sqlalchemy.orm import sessionmaker
            from database.models import Product, Customer, Supplier
            from datetime import datetime, timedelta

            Session = sessionmaker(bind=self.engine)
            with Session() as session:
                # تنبيهات المخزون المنخفض
                low_stock_products = session.query(Product).filter(
                    Product.quantity <= Product.min_quantity,
                    Product.min_quantity > 0  # فقط المنتجات التي لها حد أدنى محدد
                ).all()

                for product in low_stock_products:
                    self.add_notification(
                        "⚠️ مخزون منخفض",
                        f"المنتج '{product.name}' - الكمية المتبقية: {product.quantity} (الحد الأدنى: {product.min_quantity})",
                        "#fff3cd",  # أصفر فاتح
                        "warning"
                    )

                # تنبيهات العملاء المدينين
                debtors = session.query(Customer).filter(
                    Customer.balance < 0
                ).all()

                for customer in debtors:
                    self.add_notification(
                        "💰 عميل مدين",
                        f"العميل '{customer.name}' - المبلغ المستحق: {abs(customer.balance):,.2f} جنيه",
                        "#f8d7da",  # أحمر فاتح
                        "debt"
                    )

                # تنبيهات الموردين الدائنين
                creditors = session.query(Supplier).filter(
                    Supplier.balance > 0
                ).all()

                for supplier in creditors:
                    self.add_notification(
                        "💳 مستحقات مورد",
                        f"المورد '{supplier.name}' - المبلغ المستحق: {supplier.balance:,.2f} جنيه",
                        "#d1ecf1",  # أزرق فاتح
                        "credit"
                    )

                # إذا لم توجد تنبيهات
                if (not low_stock_products and not debtors and not creditors):
                    self.add_notification(
                        "✅ لا توجد تنبيهات",
                        "جميع الأمور تسير بشكل طبيعي",
                        "#d4edda",  # أخضر فاتح
                        "success"
                    )

        except Exception as e:
            self.add_notification(
                "❌ خطأ",
                f"حدث خطأ أثناء تحميل التنبيهات: {str(e)}",
                "#f8d7da",  # أحمر فاتح
                "error"
            )

    def add_notification(self, title, message, bg_color, notification_type="info"):
        """إضافة تنبيه جديد"""
        notification_frame = QFrame()
        notification_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {bg_color};
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
        """)

        layout = QVBoxLayout(notification_frame)
        layout.setSpacing(5)

        # عنوان التنبيه
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
            }
        """)
        layout.addWidget(title_label)

        # نص التنبيه
        message_label = QLabel(message)
        message_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                line-height: 1.4;
            }
        """)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # وقت التنبيه
        time_label = QLabel(f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        time_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #868e96;
                font-style: italic;
            }
        """)
        layout.addWidget(time_label)

        self.notifications_layout.addWidget(notification_frame)