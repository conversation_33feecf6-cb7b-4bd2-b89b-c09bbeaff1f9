import cv2
import numpy as np
from pyzbar import pyzbar
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QMessageBox, QFrame)
from PyQt5.QtCore import QTimer, Qt, pyqtSignal
from PyQt5.QtGui import QImage, QPixmap, QFont
import threading
import time

class BarcodeScannerDialog(QDialog):
    barcode_detected = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("قارئ الباركود")
        self.setModal(True)

        # تعيين الحجم المطلوب الجديد (طولي)
        self.setMinimumSize(650, 1200)  # حد أدنى مناسب للشكل الطولي
        self.resize(750, 1400)  # الحجم الجديد المطلوب 750×1400 (طولي)

        # إضافة أزرار التحكم في النافذة (تكبير/تصغير)
        self.setWindowFlags(Qt.Dialog | Qt.WindowMinMaxButtonsHint | Qt.WindowCloseButtonHint)
        
        # متغيرات الكاميرا
        self.camera = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        self.scanning = False
        self.last_barcode = ""
        self.last_scan_time = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        self.setStyleSheet("""
            QDialog {
                background-color: #2C3E50;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 16px;
            }
            QPushButton {
                background-color: #3498DB;
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 18px 35px;
                border: none;
                border-radius: 10px;
                min-width: 150px;
                min-height: 55px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
            QPushButton:pressed {
                background-color: #21618C;
            }
            QFrame {
                border: 3px solid #34495E;
                border-radius: 10px;
                background-color: #34495E;
            }
        """)
        
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # عنوان النافذة
        title_label = QLabel("📷 قارئ الباركود")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 32px;
            font-weight: bold;
            color: #ECF0F1;
            margin: 25px 0;
            padding: 15px;
        """)
        layout.addWidget(title_label)
        
        # إطار عرض الكاميرا
        camera_frame = QFrame()
        camera_layout = QVBoxLayout()
        camera_frame.setLayout(camera_layout)
        
        self.camera_label = QLabel("📹 انتظار تشغيل الكاميرا...")
        self.camera_label.setAlignment(Qt.AlignCenter)
        self.camera_label.setMinimumSize(600, 700)  # حجم مناسب للعرض الأصغر
        self.camera_label.setStyleSheet("""
            background-color: #1A252F;
            border: 2px dashed #7F8C8D;
            border-radius: 8px;
            font-size: 18px;
            color: #BDC3C7;
        """)
        camera_layout.addWidget(self.camera_label)
        
        layout.addWidget(camera_frame)
        
        # منطقة عرض النتيجة
        result_frame = QFrame()
        result_frame.setStyleSheet("""
            QFrame {
                background-color: #1A252F;
                border: 2px solid #27AE60;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        result_layout = QVBoxLayout()
        result_frame.setLayout(result_layout)
        
        result_title = QLabel("📊 نتيجة المسح:")
        result_title.setStyleSheet("font-size: 22px; font-weight: bold; color: #27AE60; padding: 8px;")
        result_layout.addWidget(result_title)

        self.result_label = QLabel("لم يتم العثور على باركود بعد...")
        self.result_label.setStyleSheet("""
            font-size: 20px;
            color: #ECF0F1;
            background-color: #2C3E50;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #34495E;
            min-height: 50px;
        """)
        self.result_label.setWordWrap(True)
        result_layout.addWidget(self.result_label)
        
        layout.addWidget(result_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🎥 تشغيل الكاميرا")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.start_btn.clicked.connect(self.start_camera)
        
        self.stop_btn = QPushButton("⏹️ إيقاف الكاميرا")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_camera)
        self.stop_btn.setEnabled(False)
        
        self.use_btn = QPushButton("✅ استخدام الباركود")
        self.use_btn.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
            }
            QPushButton:hover {
                background-color: #E67E22;
            }
        """)
        self.use_btn.clicked.connect(self.use_barcode)
        self.use_btn.setEnabled(False)
        
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
            }
        """)
        self.close_btn.clicked.connect(self.close_scanner)
        
        buttons_layout.addWidget(self.start_btn)
        buttons_layout.addWidget(self.stop_btn)
        buttons_layout.addWidget(self.use_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
        # تعليمات الاستخدام
        instructions = QLabel("""
        📋 تعليمات الاستخدام:
        1️⃣ اضغط على "تشغيل الكاميرا" لبدء المسح
        2️⃣ وجه الكاميرا نحو الباركود
        3️⃣ انتظر حتى يتم اكتشاف الباركود تلقائياً
        4️⃣ اضغط على "استخدام الباركود" لإضافته للمنتج
        """)
        instructions.setStyleSheet("""
            background-color: #34495E;
            padding: 20px;
            border-radius: 10px;
            font-size: 18px;
            color: #BDC3C7;
            border: 2px solid #7F8C8D;
            line-height: 2.0;
        """)
        layout.addWidget(instructions)
        
    def start_camera(self):
        try:
            # محاولة فتح الكاميرا
            self.camera = cv2.VideoCapture(0)
            if not self.camera.isOpened():
                QMessageBox.warning(self, "خطأ", "لا يمكن الوصول للكاميرا!\nتأكد من توصيل الكاميرا وعدم استخدامها من برنامج آخر.")
                return
                
            # تعيين دقة الكاميرا لتتناسب مع الحجم الجديد
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 600)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 700)
            
            self.scanning = True
            self.timer.start(30)  # تحديث كل 30 مللي ثانية
            
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.result_label.setText("🔍 جاري البحث عن باركود...")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تشغيل الكاميرا:\n{str(e)}")
            
    def stop_camera(self):
        self.scanning = False
        self.timer.stop()
        
        if self.camera:
            self.camera.release()
            self.camera = None
            
        self.camera_label.setText("📹 تم إيقاف الكاميرا")
        self.camera_label.setPixmap(QPixmap())
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
    def update_frame(self):
        if not self.camera or not self.scanning:
            return
            
        ret, frame = self.camera.read()
        if not ret:
            return
            
        # قلب الصورة أفقياً لتبدو كالمرآة
        frame = cv2.flip(frame, 1)
        
        # البحث عن باركود
        barcodes = pyzbar.decode(frame)
        
        for barcode in barcodes:
            # رسم مربع حول الباركود
            (x, y, w, h) = barcode.rect
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 3)
            
            # استخراج نص الباركود
            barcode_data = barcode.data.decode('utf-8')
            barcode_type = barcode.type
            
            # عرض معلومات الباركود على الصورة
            text = f"{barcode_type}: {barcode_data}"
            cv2.putText(frame, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # تجنب المسح المتكرر لنفس الباركود
            current_time = time.time()
            if barcode_data != self.last_barcode or (current_time - self.last_scan_time) > 2:
                self.last_barcode = barcode_data
                self.last_scan_time = current_time
                self.result_label.setText(f"✅ تم العثور على باركود:\n{barcode_data}")
                self.use_btn.setEnabled(True)
                
        # تحويل الصورة لعرضها في PyQt
        rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        
        # عرض الصورة
        pixmap = QPixmap.fromImage(qt_image)
        scaled_pixmap = pixmap.scaled(self.camera_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.camera_label.setPixmap(scaled_pixmap)
        
    def use_barcode(self):
        if self.last_barcode:
            self.barcode_detected.emit(self.last_barcode)
            self.accept()
            
    def close_scanner(self):
        self.stop_camera()
        self.reject()
        
    def closeEvent(self, event):
        self.stop_camera()
        event.accept()
